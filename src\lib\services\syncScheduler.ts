/**
 * Sync Scheduler
 * Automatically runs sync operations at regular intervals
 */

import { hybridSyncService } from "./hybridSyncService";

class SyncScheduler {
  private syncInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  /**
   * Start the sync scheduler
   */
  start() {
    if (this.isRunning) {
      console.log('⚠️ Sync scheduler is already running');
      return;
    }

    console.log('🚀 Starting sync scheduler...');
    this.isRunning = true;

    // Sync recent data every 5 minutes
    this.syncInterval = setInterval(async () => {
      try {
        console.log('🔄 Running scheduled sync...');
        await this.performQuickSync();
      } catch (error) {
        console.error('❌ Scheduled sync failed:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Cleanup old data every hour
    this.cleanupInterval = setInterval(async () => {
      try {
        console.log('🧹 Running scheduled cleanup...');
        await this.performCleanup();
      } catch (error) {
        console.error('❌ Scheduled cleanup failed:', error);
      }
    }, 60 * 60 * 1000); // 1 hour

    // Run initial sync
    this.performQuickSync().catch(error => {
      console.error('❌ Initial sync failed:', error);
    });

    console.log('✅ Sync scheduler started successfully');
  }

  /**
   * Stop the sync scheduler
   */
  stop() {
    if (!this.isRunning) {
      console.log('⚠️ Sync scheduler is not running');
      return;
    }

    console.log('🛑 Stopping sync scheduler...');

    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    this.isRunning = false;
    console.log('✅ Sync scheduler stopped');
  }

  /**
   * Quick sync - only recent data (last 2 hours)
   */
  private async performQuickSync() {
    const messageSync = await hybridSyncService.syncRecentMessages(2);
    const notificationSync = await hybridSyncService.syncRecentNotifications(2);

    const totalSynced = messageSync.synced + notificationSync.synced;
    const totalErrors = messageSync.errors.length + notificationSync.errors.length;

    if (totalSynced > 0) {
      console.log(`📊 Quick sync completed: ${totalSynced} items synced`);
    }

    if (totalErrors > 0) {
      console.error(`⚠️ Quick sync had ${totalErrors} errors`);
    }
  }

  /**
   * Cleanup old data
   */
  private async performCleanup() {
    const cleanup = await hybridSyncService.cleanupOldData();
    
    const totalDeleted = cleanup.messagesDeleted + cleanup.notificationsDeleted;
    
    if (totalDeleted > 0) {
      console.log(`🗑️ Cleanup completed: ${totalDeleted} old items removed`);
    }

    if (cleanup.errors.length > 0) {
      console.error(`⚠️ Cleanup had ${cleanup.errors.length} errors`);
    }
  }

  /**
   * Get scheduler status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      hasSyncInterval: this.syncInterval !== null,
      hasCleanupInterval: this.cleanupInterval !== null,
    };
  }

  /**
   * Force a full sync
   */
  async forceFullSync() {
    console.log('🔄 Forcing full sync...');
    try {
      const stats = await hybridSyncService.performFullSync();
      console.log('✅ Forced full sync completed:', stats);
      return stats;
    } catch (error) {
      console.error('❌ Forced full sync failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const syncScheduler = new SyncScheduler();

// Auto-start in production
if (process.env.NODE_ENV === 'production') {
  // Start scheduler after a short delay to ensure everything is initialized
  setTimeout(() => {
    syncScheduler.start();
  }, 10000); // 10 seconds delay
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('📡 Received SIGTERM, stopping sync scheduler...');
  syncScheduler.stop();
});

process.on('SIGINT', () => {
  console.log('📡 Received SIGINT, stopping sync scheduler...');
  syncScheduler.stop();
});
