"use client";

import Image from "next/image";
import Link from "next/link";
import {
  UserPlusIcon,
  GiftIcon,
  EllipsisHorizontalIcon,
  XMarkIcon,
  UserIcon,
  VideoCameraIcon,
  ChatBubbleLeftRightIcon
} from "@heroicons/react/24/outline";
import {
  UserGroupIcon,
  CircleStackIcon
} from "@heroicons/react/24/solid";

import { PeopleYouMayKnow } from "./PeopleYouMayKnow";
import { Birthdays } from "./Birthdays";
import { Contacts } from "./Contacts";


export function RightSidebar() {

  return (
    <div className="block w-full">
      <div className="relative lg:fixed lg:top-[5rem] lg:w-[calc(25%-1rem)] space-y-5 pl-2 overflow-y-auto lg:h-[calc(100vh-5rem)] scrollbar-none">
        {/* All content is now fixed and scrollable */}
        <div className="space-y-5">




          {/* Birthdays Section */}
          <Birthdays />
        </div>

        {/* Additional content */}
        <div className="space-y-5">
          {/* People You May Know Section */}
          <PeopleYouMayKnow />





          {/* Contacts Section */}
          <Contacts />
        </div>
      </div>
    </div>
  );
}
