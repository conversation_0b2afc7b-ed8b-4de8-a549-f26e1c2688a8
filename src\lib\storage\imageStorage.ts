/**
 * Image Storage Service
 * Handles deletion of images from various storage providers
 */

import { unlink } from 'fs/promises';
import { join } from 'path';

export interface ImageDeletionResult {
  success: boolean;
  deletedImages: string[];
  failedImages: string[];
  errors: string[];
}

/**
 * Extract filename from URL
 */
function extractFilenameFromUrl(url: string): string | null {
  try {
    // Handle different URL formats
    if (url.startsWith('/uploads/')) {
      // Local storage: /uploads/images/filename.jpg
      return url.split('/').pop() || null;
    } else if (url.includes('cloudinary.com')) {
      // Cloudinary: extract public_id
      const matches = url.match(/\/v\d+\/(.+)\./);
      return matches ? matches[1] : null;
    } else if (url.includes('amazonaws.com') || url.includes('s3.')) {
      // AWS S3: extract key
      const urlObj = new URL(url);
      return urlObj.pathname.substring(1); // Remove leading slash
    } else if (url.includes('supabase.co')) {
      // Supabase Storage
      const matches = url.match(/\/storage\/v1\/object\/public\/[^\/]+\/(.+)/);
      return matches ? matches[1] : null;
    }
    
    // Default: try to extract filename
    return url.split('/').pop()?.split('?')[0] || null;
  } catch (error) {
    console.error('Error extracting filename from URL:', url, error);
    return null;
  }
}

/**
 * Delete image from local storage
 */
async function deleteLocalImage(filename: string): Promise<boolean> {
  try {
    const filePath = join(process.cwd(), 'public', 'uploads', 'images', filename);
    await unlink(filePath);
    return true;
  } catch (error) {
    console.error('Error deleting local image:', filename, error);
    return false;
  }
}

/**
 * Delete image from Cloudinary
 */
async function deleteCloudinaryImage(publicId: string): Promise<boolean> {
  try {
    // Only import cloudinary if we're actually using it
    const cloudinary = await import('cloudinary').then(m => m.v2);
    
    if (!cloudinary.config().cloud_name) {
      console.warn('Cloudinary not configured, skipping deletion');
      return false;
    }

    const result = await cloudinary.uploader.destroy(publicId);
    return result.result === 'ok';
  } catch (error) {
    console.error('Error deleting Cloudinary image:', publicId, error);
    return false;
  }
}

/**
 * Delete image from AWS S3
 */
async function deleteS3Image(key: string): Promise<boolean> {
  try {
    // Only import AWS SDK if we're actually using it
    const { S3Client, DeleteObjectCommand } = await import('@aws-sdk/client-s3');
    
    const s3Client = new S3Client({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
    });

    const command = new DeleteObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET || '',
      Key: key,
    });

    await s3Client.send(command);
    return true;
  } catch (error) {
    console.error('Error deleting S3 image:', key, error);
    return false;
  }
}

/**
 * Delete image from Supabase Storage
 */
async function deleteSupabaseImage(path: string): Promise<boolean> {
  try {
    // Only import Supabase if we're actually using it
    const { createClient } = await import('@supabase/supabase-js');
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );

    const { error } = await supabase.storage
      .from(process.env.SUPABASE_STORAGE_BUCKET || 'images')
      .remove([path]);

    if (error) {
      console.error('Supabase storage deletion error:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error deleting Supabase image:', path, error);
    return false;
  }
}

/**
 * Delete a single image based on its URL
 */
async function deleteSingleImage(imageUrl: string): Promise<boolean> {
  const filename = extractFilenameFromUrl(imageUrl);
  if (!filename) {
    console.error('Could not extract filename from URL:', imageUrl);
    return false;
  }

  // Determine storage provider and delete accordingly
  if (imageUrl.startsWith('/uploads/')) {
    // Local storage
    return await deleteLocalImage(filename);
  } else if (imageUrl.includes('cloudinary.com')) {
    // Cloudinary
    return await deleteCloudinaryImage(filename);
  } else if (imageUrl.includes('amazonaws.com') || imageUrl.includes('s3.')) {
    // AWS S3
    return await deleteS3Image(filename);
  } else if (imageUrl.includes('supabase.co')) {
    // Supabase Storage
    return await deleteSupabaseImage(filename);
  } else {
    console.warn('Unknown storage provider for URL:', imageUrl);
    return false;
  }
}

/**
 * Delete multiple images from storage
 */
export async function deleteImages(imageUrls: string[]): Promise<ImageDeletionResult> {
  const result: ImageDeletionResult = {
    success: true,
    deletedImages: [],
    failedImages: [],
    errors: [],
  };

  if (!imageUrls || imageUrls.length === 0) {
    return result;
  }

  console.log(`🗑️ Deleting ${imageUrls.length} images from storage...`);

  // Process deletions in parallel for better performance
  const deletionPromises = imageUrls.map(async (imageUrl) => {
    try {
      const success = await deleteSingleImage(imageUrl);
      if (success) {
        result.deletedImages.push(imageUrl);
        console.log(`✅ Deleted image: ${imageUrl}`);
      } else {
        result.failedImages.push(imageUrl);
        result.errors.push(`Failed to delete: ${imageUrl}`);
        console.error(`❌ Failed to delete image: ${imageUrl}`);
      }
    } catch (error) {
      result.failedImages.push(imageUrl);
      const errorMessage = `Error deleting ${imageUrl}: ${error instanceof Error ? error.message : 'Unknown error'}`;
      result.errors.push(errorMessage);
      console.error(`❌ ${errorMessage}`);
    }
  });

  await Promise.all(deletionPromises);

  // Update overall success status
  result.success = result.failedImages.length === 0;

  console.log(`🏁 Image deletion completed: ${result.deletedImages.length} deleted, ${result.failedImages.length} failed`);

  return result;
}

/**
 * Delete images and videos from storage
 */
export async function deleteMediaFiles(images: string[] = [], videos: string[] = []): Promise<ImageDeletionResult> {
  // For now, we only handle images. Videos can be added later with similar logic
  const allMediaUrls = [...images];
  
  if (videos.length > 0) {
    console.log(`⚠️ Video deletion not implemented yet. Skipping ${videos.length} videos.`);
  }

  return await deleteImages(allMediaUrls);
}
