/**
 * Hybrid Sync Service
 * Manages synchronization between MySQL (primary) and Supabase (real-time)
 * Ensures data consistency and handles cleanup operations
 */

import { db } from "@/lib/db";
import { messages, notifications, users } from "@/lib/db/schema";
import { getSupabaseServiceClient } from "@/lib/supabase/client";
import { eq, and, gte, lte } from "drizzle-orm";

export interface SyncStats {
  messagesSynced: number;
  notificationsSynced: number;
  messagesCleanedUp: number;
  notificationsCleanedUp: number;
  errors: string[];
}

class HybridSyncService {
  private supabase = getSupabaseServiceClient();

  /**
   * Sync recent messages from MySQL to Supabase
   * Only syncs messages from the last 24 hours to keep real-time data fresh
   */
  async syncRecentMessages(hoursBack: number = 24): Promise<{ synced: number; errors: string[] }> {
    const errors: string[] = [];
    let synced = 0;

    try {
      const cutoffTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000);

      // Get recent messages from MySQL
      const recentMessages = await db.query.messages.findMany({
        where: gte(messages.createdAt, cutoffTime),
        with: {
          sender: {
            columns: { id: true, name: true, image: true }
          },
          receiver: {
            columns: { id: true, name: true, image: true }
          }
        }
      });

      // Check which messages are already in Supabase
      const { data: existingMessages } = await this.supabase
        .from('messages_realtime')
        .select('mysql_message_id')
        .in('mysql_message_id', recentMessages.map(m => m.id));

      const existingIds = new Set(existingMessages?.map(m => m.mysql_message_id) || []);

      // Sync missing messages
      const messagesToSync = recentMessages.filter(m => !existingIds.has(m.id));

      for (const message of messagesToSync) {
        try {
          await this.supabase.from('messages_realtime').insert({
            mysql_message_id: message.id,
            sender_id: message.senderId,
            receiver_id: message.receiverId,
            content: message.content,
            created_at: message.createdAt.toISOString(),
            read_at: message.read ? message.createdAt.toISOString() : null,
          });
          synced++;
        } catch (error) {
          errors.push(`Failed to sync message ${message.id}: ${error}`);
        }
      }

    } catch (error) {
      errors.push(`Failed to sync messages: ${error}`);
    }

    return { synced, errors };
  }

  /**
   * Sync recent notifications from MySQL to Supabase
   */
  async syncRecentNotifications(hoursBack: number = 24): Promise<{ synced: number; errors: string[] }> {
    const errors: string[] = [];
    let synced = 0;

    try {
      const cutoffTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000);

      // Get recent notifications from MySQL
      const recentNotifications = await db.query.notifications.findMany({
        where: gte(notifications.createdAt, cutoffTime),
        with: {
          sender: {
            columns: { id: true, name: true, image: true }
          },
          recipient: {
            columns: { id: true, name: true, image: true }
          }
        }
      });

      // Check which notifications are already in Supabase
      const { data: existingNotifications } = await this.supabase
        .from('notifications_realtime')
        .select('mysql_notification_id')
        .in('mysql_notification_id', recentNotifications.map(n => n.id));

      const existingIds = new Set(existingNotifications?.map(n => n.mysql_notification_id) || []);

      // Sync missing notifications
      const notificationsToSync = recentNotifications.filter(n => !existingIds.has(n.id));

      for (const notification of notificationsToSync) {
        try {
          await this.supabase.from('notifications_realtime').insert({
            mysql_notification_id: notification.id,
            recipient_id: notification.recipientId,
            type: notification.type,
            data: {
              senderId: notification.senderId,
              postId: notification.postId,
              commentId: notification.commentId,
              messageId: notification.messageId,
              groupId: notification.groupId,
              eventId: notification.eventId,
              fanPageId: notification.fanPageId,
              subscriptionId: notification.subscriptionId,
              sender: notification.sender,
            },
            created_at: notification.createdAt.toISOString(),
            read_at: notification.read ? notification.createdAt.toISOString() : null,
          });
          synced++;
        } catch (error) {
          errors.push(`Failed to sync notification ${notification.id}: ${error}`);
        }
      }

    } catch (error) {
      errors.push(`Failed to sync notifications: ${error}`);
    }

    return { synced, errors };
  }

  /**
   * Clean up old real-time data
   * Messages older than 7 days, notifications older than 30 days
   */
  async cleanupOldData(): Promise<{ messagesDeleted: number; notificationsDeleted: number; errors: string[] }> {
    const errors: string[] = [];
    let messagesDeleted = 0;
    let notificationsDeleted = 0;

    try {
      // Clean up old messages (7 days)
      const messagesCutoff = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const { count: deletedMessages, error: messagesError } = await this.supabase
        .from('messages_realtime')
        .delete()
        .lt('created_at', messagesCutoff.toISOString());

      if (messagesError) {
        errors.push(`Failed to cleanup messages: ${messagesError.message}`);
      } else {
        messagesDeleted = deletedMessages || 0;
      }

      // Clean up old notifications (30 days)
      const notificationsCutoff = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const { count: deletedNotifications, error: notificationsError } = await this.supabase
        .from('notifications_realtime')
        .delete()
        .lt('created_at', notificationsCutoff.toISOString());

      if (notificationsError) {
        errors.push(`Failed to cleanup notifications: ${notificationsError.message}`);
      } else {
        notificationsDeleted = deletedNotifications || 0;
      }

    } catch (error) {
      errors.push(`Failed to cleanup old data: ${error}`);
    }

    return { messagesDeleted, notificationsDeleted, errors };
  }

  /**
   * Sync user presence data
   * Updates online status for active users
   */
  async syncUserPresence(activeUserIds: string[]): Promise<{ updated: number; errors: string[] }> {
    const errors: string[] = [];
    let updated = 0;

    try {
      for (const userId of activeUserIds) {
        try {
          await this.supabase.rpc('update_user_presence', {
            p_user_id: userId,
            p_status: 'online',
          });
          updated++;
        } catch (error) {
          errors.push(`Failed to update presence for user ${userId}: ${error}`);
        }
      }
    } catch (error) {
      errors.push(`Failed to sync user presence: ${error}`);
    }

    return { updated, errors };
  }

  /**
   * Full sync operation
   * Syncs recent data and cleans up old data
   */
  async performFullSync(): Promise<SyncStats> {
    console.log('🔄 Starting hybrid sync operation...');

    const stats: SyncStats = {
      messagesSynced: 0,
      notificationsSynced: 0,
      messagesCleanedUp: 0,
      notificationsCleanedUp: 0,
      errors: [],
    };

    // Sync recent messages
    const messageSync = await this.syncRecentMessages(24);
    stats.messagesSynced = messageSync.synced;
    stats.errors.push(...messageSync.errors);

    // Sync recent notifications
    const notificationSync = await this.syncRecentNotifications(24);
    stats.notificationsSynced = notificationSync.synced;
    stats.errors.push(...notificationSync.errors);

    // Cleanup old data
    const cleanup = await this.cleanupOldData();
    stats.messagesCleanedUp = cleanup.messagesDeleted;
    stats.notificationsCleanedUp = cleanup.notificationsDeleted;
    stats.errors.push(...cleanup.errors);

    console.log('✅ Hybrid sync completed:', stats);
    return stats;
  }

  /**
   * Health check for sync system
   */
  async healthCheck(): Promise<{ healthy: boolean; issues: string[] }> {
    const issues: string[] = [];

    try {
      // Test Supabase connection
      const { error: supabaseError } = await this.supabase
        .from('user_presence')
        .select('count')
        .limit(1);

      if (supabaseError) {
        issues.push(`Supabase connection failed: ${supabaseError.message}`);
      }

      // Test MySQL connection
      try {
        await db.query.users.findFirst({
          columns: { id: true },
          limit: 1,
        });
      } catch (mysqlError) {
        issues.push(`MySQL connection failed: ${mysqlError}`);
      }

      // Check for data consistency
      const recentMessages = await db.query.messages.findMany({
        where: gte(messages.createdAt, new Date(Date.now() - 60 * 60 * 1000)), // Last hour
        columns: { id: true },
        limit: 10,
      });

      if (recentMessages.length > 0) {
        const { data: supabaseMessages } = await this.supabase
          .from('messages_realtime')
          .select('mysql_message_id')
          .in('mysql_message_id', recentMessages.map(m => m.id));

        const syncRatio = (supabaseMessages?.length || 0) / recentMessages.length;
        if (syncRatio < 0.8) {
          issues.push(`Low sync ratio detected: ${Math.round(syncRatio * 100)}%`);
        }
      }

    } catch (error) {
      issues.push(`Health check failed: ${error}`);
    }

    return {
      healthy: issues.length === 0,
      issues,
    };
  }
}

// Export singleton instance
export const hybridSyncService = new HybridSyncService();
