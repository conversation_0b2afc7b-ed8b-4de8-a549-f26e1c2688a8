"use client";

import { useEffect, useState } from "react";

/**
 * Hook to safely check if we're on the client side
 * Prevents hydration mismatches when using browser-specific APIs
 */
export function useClientSide() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook to safely get window location
 * Returns empty string during SSR to prevent hydration mismatches
 */
export function useWindowLocation() {
  const [location, setLocation] = useState('');
  const isClient = useClientSide();

  useEffect(() => {
    if (isClient && typeof window !== 'undefined') {
      setLocation(window.location.href);
    }
  }, [isClient]);

  return location;
}

/**
 * Hook to safely detect mobile view
 * Prevents hydration mismatches from window.innerWidth checks
 */
export function useMobileView(breakpoint: number = 1024) {
  const [isMobile, setIsMobile] = useState(false);
  const isClient = useClientSide();

  useEffect(() => {
    if (!isClient) return;

    const checkMobile = () => {
      setIsMobile(window.innerWidth < breakpoint);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [isClient, breakpoint]);

  return isMobile;
}
