@import "tailwindcss";
@import "../styles/notifications.css";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: system-ui, monospace;
}

html {
  scroll-behavior: smooth;
}

/* Prevent HeadlessUI from hiding scrollbar when Menu dropdowns are opened */
html:has([role="menu"]) {
  overflow: auto !important;
  padding-right: 0 !important;
}

/* Fallback for browsers that don't support :has() */
@supports not (selector(:has(*))) {
  /* Override HeadlessUI's automatic overflow hidden for all cases except when explicitly needed */
  html {
    overflow: auto !important;
  }

  /* Only hide overflow when a modal dialog is present */
  html[data-headlessui-state~="open"] {
    overflow: auto !important;
    padding-right: 0 !important;
  }
}

/* Allow scrollbar hiding only for Dialog components (modals) */
html:has([role="dialog"]) {
  overflow: hidden;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Enhanced Like/Dislike Button Animations */
@keyframes likePress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1.05); }
}

@keyframes dislikePress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1.05); }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  14% { transform: scale(1.1); }
  28% { transform: scale(1); }
  42% { transform: scale(1.1); }
  70% { transform: scale(1); }
}

.like-animation {
  animation: likePress 0.25s ease-out, heartBeat 0.6s ease-in-out 0.25s;
}

.dislike-animation {
  animation: dislikePress 0.25s ease-out;
}

.ripple-effect {
  animation: ripple 0.6s ease-out;
}

/* Smooth transitions for all interactive elements */
.smooth-transition {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced button hover effects */
.button-hover-scale:hover {
  transform: scale(1.02);
}

.button-hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Custom scrollbar utilities */
.scrollbar-none {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
}

.scrollbar-none::-webkit-scrollbar {
  display: none;             /* Chrome, Safari and Opera */
}

.scrollbar-thin {
  scrollbar-width: thin;
}

/* Messages page specific styles */
.messages-container {
  height: calc(100vh - 280px);
  min-height: 500px;
}

.messages-chat-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.messages-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  scroll-behavior: smooth;
}

.conversation-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.conversation-list-items {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* Custom scrollbar styles */
.messages-list::-webkit-scrollbar,
.conversation-list-items::-webkit-scrollbar {
  width: 6px;
}

.messages-list::-webkit-scrollbar-track,
.conversation-list-items::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb,
.conversation-list-items::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb:hover,
.conversation-list-items::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Conversation Info scrollbar */
.conversation-info-scroll {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.conversation-info-scroll::-webkit-scrollbar {
  width: 6px;
}

.conversation-info-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.conversation-info-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.conversation-info-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* PIN Setup Modal specific styles */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-enter {
  animation: modalSlideIn 0.3s ease-out;
}

/* PIN input focus animation */
@keyframes pinFocus {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pin-focus {
  animation: pinFocus 0.2s ease-in-out;
}

/* Custom animations for blog cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient text utilities */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Custom line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

/* Text shadow utility */
.shadow-text {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

/* FullCalendar Styles */
.fc .fc-toolbar-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.fc .fc-button {
  background-color: #f3f4f6;
  border-color: #e5e7eb;
  color: #374151;
}

.fc .fc-button:hover {
  background-color: #e5e7eb;
  border-color: #d1d5db;
  color: #111827;
}

.fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.fc-event {
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
}

.fc-event:hover {
  opacity: 0.9;
}

.fc-daygrid-day-number,
.fc-col-header-cell-cushion {
  color: #374151;
  text-decoration: none !important;
}

.fc-day-today {
  background-color: #eff6ff !important;
}

.fc-day-today .fc-daygrid-day-number {
  color: #3b82f6;
  font-weight: 600;
}

.fc-h-event .fc-event-title {
  font-weight: 500;
}

.fc-list-day-cushion {
  background-color: #f3f4f6 !important;
}

.fc-list-event:hover td {
  background-color: #eff6ff !important;
}

.fc-theme-standard .fc-list-day-cushion {
  background-color: #f9fafb;
}

.fc .fc-timegrid-slot {
  height: 2.5em;
}

.fc .fc-timegrid-axis-cushion,
.fc .fc-timegrid-slot-label-cushion {
  color: #6b7280;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .fc .fc-toolbar {
    flex-direction: column;
    gap: 0.5rem;
  }

  .fc .fc-toolbar-title {
    font-size: 1rem;
  }

  .fc .fc-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}
