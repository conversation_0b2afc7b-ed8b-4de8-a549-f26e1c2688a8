/**
 * Real-time Message Service using Supabase
 * Replaces Socket.IO functionality for real-time messaging
 */

import { getSupabaseClient, getSupabaseServiceClient, MessageRealtime } from '../supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface MessageEvent {
  type: 'new_message' | 'message_read' | 'typing_start' | 'typing_stop';
  data: any;
  userId?: string;
  conversationId?: string;
}

export interface TypingIndicator {
  userId: string;
  userName: string;
  conversationId: string;
  isTyping: boolean;
}

class RealtimeMessageService {
  private supabase = getSupabaseClient();
  private serviceSupabase = getSupabaseServiceClient();
  private channels: Map<string, RealtimeChannel> = new Map();
  private messageListeners: Map<string, (event: MessageEvent) => void> = new Map();
  private typingListeners: Map<string, (typing: TypingIndicator[]) => void> = new Map();
  private currentUserId: string | null = null;
  private typingTimeouts: Map<string, NodeJS.Timeout> = new Map();

  /**
   * Initialize the service with current user
   */
  initialize(userId: string) {
    this.currentUserId = userId;
    this.updateUserPresence('online');
    
    // Set up presence heartbeat
    setInterval(() => {
      if (this.currentUserId) {
        this.updateUserPresence('online');
      }
    }, 30000); // Update every 30 seconds
  }

  /**
   * Subscribe to messages for a specific conversation
   */
  subscribeToConversation(conversationId: string, onMessage: (event: MessageEvent) => void) {
    if (!this.currentUserId) {
      console.error('User not initialized');
      return;
    }

    const channelName = `conversation:${conversationId}`;
    
    // Remove existing subscription if any
    this.unsubscribeFromConversation(conversationId);

    const channel = this.supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages_realtime',
          filter: `sender_id=eq.${conversationId.split('_')[0]},receiver_id=eq.${conversationId.split('_')[1]}`,
        },
        (payload) => {
          const message = payload.new as MessageRealtime;
          onMessage({
            type: 'new_message',
            data: message,
            conversationId,
          });
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages_realtime',
          filter: `sender_id=eq.${conversationId.split('_')[0]},receiver_id=eq.${conversationId.split('_')[1]}`,
        },
        (payload) => {
          const message = payload.new as MessageRealtime;
          if (message.read_at) {
            onMessage({
              type: 'message_read',
              data: message,
              conversationId,
            });
          }
        }
      )
      .subscribe();

    this.channels.set(conversationId, channel);
    this.messageListeners.set(conversationId, onMessage);
  }

  /**
   * Subscribe to typing indicators for a conversation
   */
  subscribeToTyping(conversationId: string, onTyping: (typing: TypingIndicator[]) => void) {
    if (!this.currentUserId) return;

    const channel = this.supabase
      .channel(`typing:${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_presence',
          filter: `typing_to=eq.${conversationId}`,
        },
        async () => {
          // Fetch current typing users
          const typingUsers = await this.getTypingUsers(conversationId);
          onTyping(typingUsers);
        }
      )
      .subscribe();

    this.channels.set(`typing:${conversationId}`, channel);
    this.typingListeners.set(conversationId, onTyping);
  }

  /**
   * Send a real-time message
   */
  async sendRealtimeMessage(
    mysqlMessageId: string,
    senderId: string,
    receiverId: string,
    content: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await this.serviceSupabase
        .from('messages_realtime')
        .insert({
          mysql_message_id: mysqlMessageId,
          sender_id: senderId,
          receiver_id: receiverId,
          content,
        });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(mysqlMessageId: string): Promise<void> {
    try {
      await this.serviceSupabase
        .from('messages_realtime')
        .update({ read_at: new Date().toISOString() })
        .eq('mysql_message_id', mysqlMessageId);
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  }

  /**
   * Start typing indicator
   */
  async startTyping(conversationId: string): Promise<void> {
    if (!this.currentUserId) return;

    try {
      await this.serviceSupabase.rpc('update_user_presence', {
        p_user_id: this.currentUserId,
        p_status: 'online',
      });

      await this.serviceSupabase
        .from('user_presence')
        .update({
          typing_to: conversationId,
          typing_at: new Date().toISOString(),
        })
        .eq('user_id', this.currentUserId);

      // Auto-stop typing after 3 seconds
      const timeoutKey = `${this.currentUserId}:${conversationId}`;
      if (this.typingTimeouts.has(timeoutKey)) {
        clearTimeout(this.typingTimeouts.get(timeoutKey)!);
      }

      const timeout = setTimeout(() => {
        this.stopTyping(conversationId);
      }, 3000);

      this.typingTimeouts.set(timeoutKey, timeout);
    } catch (error) {
      console.error('Error starting typing:', error);
    }
  }

  /**
   * Stop typing indicator
   */
  async stopTyping(conversationId: string): Promise<void> {
    if (!this.currentUserId) return;

    try {
      await this.serviceSupabase
        .from('user_presence')
        .update({
          typing_to: null,
          typing_at: null,
        })
        .eq('user_id', this.currentUserId);

      const timeoutKey = `${this.currentUserId}:${conversationId}`;
      if (this.typingTimeouts.has(timeoutKey)) {
        clearTimeout(this.typingTimeouts.get(timeoutKey)!);
        this.typingTimeouts.delete(timeoutKey);
      }
    } catch (error) {
      console.error('Error stopping typing:', error);
    }
  }

  /**
   * Update user presence status
   */
  async updateUserPresence(status: 'online' | 'offline' | 'away'): Promise<void> {
    if (!this.currentUserId) return;

    try {
      await this.serviceSupabase.rpc('update_user_presence', {
        p_user_id: this.currentUserId,
        p_status: status,
      });
    } catch (error) {
      console.error('Error updating presence:', error);
    }
  }

  /**
   * Get typing users for a conversation
   */
  private async getTypingUsers(conversationId: string): Promise<TypingIndicator[]> {
    try {
      const { data, error } = await this.supabase
        .from('user_presence')
        .select('user_id, typing_at')
        .eq('typing_to', conversationId)
        .not('typing_at', 'is', null)
        .gte('typing_at', new Date(Date.now() - 5000).toISOString()); // Last 5 seconds

      if (error || !data) return [];

      return data.map((user) => ({
        userId: user.user_id,
        userName: user.user_id, // We'll need to fetch this from MySQL if needed
        conversationId,
        isTyping: true,
      }));
    } catch (error) {
      console.error('Error fetching typing users:', error);
      return [];
    }
  }

  /**
   * Unsubscribe from conversation
   */
  unsubscribeFromConversation(conversationId: string) {
    const channel = this.channels.get(conversationId);
    if (channel) {
      this.supabase.removeChannel(channel);
      this.channels.delete(conversationId);
    }

    const typingChannel = this.channels.get(`typing:${conversationId}`);
    if (typingChannel) {
      this.supabase.removeChannel(typingChannel);
      this.channels.delete(`typing:${conversationId}`);
    }

    this.messageListeners.delete(conversationId);
    this.typingListeners.delete(conversationId);
  }

  /**
   * Cleanup all subscriptions
   */
  cleanup() {
    this.channels.forEach((channel) => {
      this.supabase.removeChannel(channel);
    });
    this.channels.clear();
    this.messageListeners.clear();
    this.typingListeners.clear();
    this.typingTimeouts.forEach((timeout) => clearTimeout(timeout));
    this.typingTimeouts.clear();

    if (this.currentUserId) {
      this.updateUserPresence('offline');
    }
  }
}

// Export singleton instance
export const realtimeMessageService = new RealtimeMessageService();
