/**
 * Server-side Notification Helper Functions
 * Database operations for creating and managing notifications
 * This file should only be imported in API routes or server-side code
 */

import { db } from "@/lib/db";
import { notifications } from "@/lib/db/schema";
import { getSupabaseServiceClient } from "@/lib/supabase/client";
import { v4 as uuidv4 } from "uuid";

export interface CreateNotificationData {
  recipientId: string;
  type: string;
  senderId?: string;
  postId?: string;
  commentId?: string;
  messageId?: string;
  groupId?: string;
  eventId?: string;
  fanPageId?: string;
  subscriptionId?: string;
  data?: any; // Additional data for real-time notification
}

/**
 * Create a notification in MySQL and broadcast it via Supabase real-time
 */
export async function createNotification(notificationData: CreateNotificationData): Promise<string> {
  const notificationId = uuidv4();

  try {
    // Insert notification into MySQL (primary storage)
    await db.insert(notifications).values({
      id: notificationId,
      recipientId: notificationData.recipientId,
      type: notificationData.type as any,
      senderId: notificationData.senderId,
      postId: notificationData.postId,
      commentId: notificationData.commentId,
      messageId: notificationData.messageId,
      groupId: notificationData.groupId,
      eventId: notificationData.eventId,
      fanPageId: notificationData.fanPageId,
      subscriptionId: notificationData.subscriptionId,
      read: false,
    });

    // Broadcast via Supabase real-time
    try {
      const supabase = getSupabaseServiceClient();
      await supabase.from('notifications_realtime').insert({
        mysql_notification_id: notificationId,
        recipient_id: notificationData.recipientId,
        type: notificationData.type,
        data: notificationData.data || {},
      });
    } catch (realtimeError) {
      console.error('Real-time notification broadcast failed:', realtimeError);
      // Don't fail the main operation if real-time fails
    }

    return notificationId;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Create multiple notifications efficiently
 */
export async function createBulkNotifications(
  notifications: CreateNotificationData[]
): Promise<string[]> {
  const notificationIds: string[] = [];

  try {
    // Prepare MySQL data
    const mysqlData = notifications.map((notif) => {
      const id = uuidv4();
      notificationIds.push(id);
      return {
        id,
        recipientId: notif.recipientId,
        type: notif.type as any,
        senderId: notif.senderId,
        postId: notif.postId,
        commentId: notif.commentId,
        messageId: notif.messageId,
        groupId: notif.groupId,
        eventId: notif.eventId,
        fanPageId: notif.fanPageId,
        subscriptionId: notif.subscriptionId,
        read: false,
      };
    });

    // Insert into MySQL
    await db.insert(notifications).values(mysqlData);

    // Prepare Supabase data
    const supabaseData = notifications.map((notif, index) => ({
      mysql_notification_id: notificationIds[index],
      recipient_id: notif.recipientId,
      type: notif.type,
      data: notif.data || {},
    }));

    // Broadcast via Supabase real-time
    try {
      const supabase = getSupabaseServiceClient();
      await supabase.from('notifications_realtime').insert(supabaseData);
    } catch (realtimeError) {
      console.error('Real-time bulk notification broadcast failed:', realtimeError);
      // Don't fail the main operation if real-time fails
    }

    return notificationIds;
  } catch (error) {
    console.error('Error creating bulk notifications:', error);
    throw error;
  }
}
