# 🚀 Supabase Real-time Setup Guide

This guide will help you set up Supabase for real-time messaging and notifications while keeping MySQL as your primary database.

## 📋 Prerequisites

1. Supabase project created
2. Environment variables configured
3. Supabase credentials available

## 🔧 Environment Variables

Add these to your `.env.local` file:

```env
# Supabase Configuration (Real-time Features Only)
NEXT_PUBLIC_SUPABASE_URL=https://xzrqnkapgxfmobrioang.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh6cnFua2FwZ3hmbW9icmlvYW5nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDY0NDAsImV4cCI6MjA2ODQyMjQ0MH0.Hz3YNL4V4-GQuAp4FcoSFffiNK9cOuMhoP4UTqcrmMk
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh6cnFua2FwZ3hmbW9icmlvYW5nIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mjg0NjQ0MCwiZXhwIjoyMDY4NDIyNDQwfQ.SH1uiO9nRlM3aVDj-v1jCNO260LdRgTjws94x6F5uRo
```

## 🗄️ Database Setup

### Option 1: Automatic Setup (Recommended)

Run the setup script:

```bash
npm run setup-supabase
```

### Option 2: Manual Setup

1. Go to your Supabase Dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `supabase/setup-realtime-tables.sql`
4. Execute the SQL

## 📊 Tables Created

The setup creates these minimal tables for real-time features:

### `messages_realtime`
- Real-time message broadcasting
- Temporary storage (7 days retention)
- Links to MySQL messages via `mysql_message_id`

### `notifications_realtime`
- Instant notification delivery
- 30 days retention
- Links to MySQL notifications via `mysql_notification_id`

### `user_presence`
- Online/offline status
- Typing indicators
- Last seen timestamps

## 🔒 Security Features

- Row Level Security (RLS) enabled
- Appropriate policies for data access
- Performance indexes
- Automatic cleanup functions

## 🔄 Data Flow

```
MySQL (Primary Storage) → API Routes → Supabase (Real-time) → Client Components
```

1. **Messages**: Saved to MySQL → Broadcasted via Supabase → Auto-cleanup after 7 days
2. **Notifications**: Persisted in MySQL → Instant delivery via Supabase → Cleanup after 30 days
3. **Presence**: Only in Supabase (temporary data)

## ✅ Verification

After setup, verify the tables exist:

1. Go to Supabase Dashboard → Table Editor
2. Check for these tables:
   - `messages_realtime`
   - `notifications_realtime`
   - `user_presence`

## 🚀 Implementation Status

1. ✅ Supabase tables created
2. ✅ Real-time services implemented
3. ✅ Message components updated
4. ✅ Notification handlers added
5. ✅ Hybrid sync system created
6. ✅ Performance optimizations added
7. ✅ Testing framework implemented

## 🧪 Testing

### Run Tests
```bash
# Test real-time functionality
npm run test-realtime

# Test data synchronization
npm run sync-data health

# Manual sync
npm run sync-data
```

### Test Results
The test suite validates:
- ✅ Supabase connection
- ✅ Database tables structure
- ✅ Real-time subscriptions
- ✅ Message functionality
- ✅ Notification functionality
- ✅ Presence functionality
- ✅ Sync functionality
- ✅ Cleanup functionality

## 🛠️ Troubleshooting

### Common Issues:

1. **RLS Policies**: If you get permission errors, check RLS policies
2. **Realtime**: Ensure tables are added to `supabase_realtime` publication
3. **Environment**: Verify all environment variables are set correctly

### Debug Commands:

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- Check realtime publication
SELECT * FROM pg_publication_tables WHERE pubname = 'supabase_realtime';

-- Test connection
SELECT NOW();
```

## 🎯 Features Implemented

### Real-time Messaging
- ✅ Instant message delivery
- ✅ Typing indicators
- ✅ Message read receipts
- ✅ Online/offline status
- ✅ Message history sync

### Real-time Notifications
- ✅ Instant notification delivery
- ✅ Browser notifications
- ✅ Toast notifications
- ✅ Notification read status
- ✅ Notification history

### User Presence
- ✅ Online/offline/away status
- ✅ Last seen timestamps
- ✅ Presence indicators
- ✅ Automatic status updates

### Performance Features
- ✅ Connection quality monitoring
- ✅ Automatic fallback to polling
- ✅ Offline message queue
- ✅ Performance metrics
- ✅ Memory optimization

## 📊 Monitoring

### Real-time Metrics
- Message delivery rate
- Connection stability
- Latency measurements
- Error rates
- User activity

### Health Checks
```bash
# Check system health
npm run sync-data health

# View sync status
curl http://localhost:3000/api/sync
```

## 🔧 Configuration

### Environment Variables
```env
# Required for real-time features
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Performance Tuning
- Adjust sync intervals in `syncScheduler.ts`
- Modify cleanup retention periods
- Configure connection quality thresholds
- Set fallback polling intervals

## 📞 Support

If you encounter issues:
1. Check Supabase logs in Dashboard
2. Verify environment variables
3. Test connection with the debug commands above
4. Run the test suite: `npm run test-realtime`
5. Check sync health: `npm run sync-data health`

## 🎉 Success!

Your MySQL + Supabase hybrid real-time system is now fully operational!

**Key Benefits:**
- 🚀 **Fast**: MySQL for complex queries, Supabase for real-time
- 💰 **Cost-effective**: Only real-time data in Supabase
- 🔄 **Reliable**: Automatic sync and fallback mechanisms
- 📱 **Modern**: Real-time messaging like WhatsApp/Telegram
- 🛡️ **Secure**: Row-level security and proper authentication
