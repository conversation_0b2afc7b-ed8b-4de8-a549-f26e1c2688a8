/**
 * Hybrid Sync API Endpoint
 * Handles synchronization between MySQL and Supabase
 */

import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { hybridSyncService } from "@/lib/services/hybridSyncService";

// GET - Health check and sync status
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    // Only allow authenticated users to check sync status
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const healthCheck = await hybridSyncService.healthCheck();

    return NextResponse.json({
      success: true,
      health: healthCheck,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error checking sync health:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to check sync health",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// POST - Trigger sync operation
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    // Only allow authenticated users to trigger sync
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { operation = "full", hoursBack = 24 } = body;

    let result;

    switch (operation) {
      case "messages":
        result = await hybridSyncService.syncRecentMessages(hoursBack);
        break;

      case "notifications":
        result = await hybridSyncService.syncRecentNotifications(hoursBack);
        break;

      case "cleanup":
        result = await hybridSyncService.cleanupOldData();
        break;

      case "full":
      default:
        result = await hybridSyncService.performFullSync();
        break;
    }

    return NextResponse.json({
      success: true,
      operation,
      result,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error performing sync operation:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to perform sync operation",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// PUT - Update user presence
export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { status = "online", userIds = [] } = body;

    // If userIds provided, sync presence for those users
    // Otherwise, just update current user's presence
    const usersToUpdate = userIds.length > 0 ? userIds : [session.user.id];

    const result = await hybridSyncService.syncUserPresence(usersToUpdate);

    return NextResponse.json({
      success: true,
      result,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error updating user presence:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to update user presence",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
