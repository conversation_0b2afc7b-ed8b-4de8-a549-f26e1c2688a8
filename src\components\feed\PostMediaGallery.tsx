"use client";

import { useState, useCallback, memo } from "react";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { cn } from "@/lib/utils";
import { PlayIcon } from "@heroicons/react/24/outline";
import {
  loadImageDimensions,
  shouldShowSeeMoreIndicator,
  getImageDisplayClasses,
  type ImageDimensions
} from "@/lib/utils/image-utils";

interface PostMediaGalleryProps {
  images?: string[] | null;
  videos?: string[] | null;
  autoPlayVideo?: boolean;
  className?: string;
  onImageError?: (imageSrc: string) => void;
  borderless?: boolean;
}

export const PostMediaGallery = memo(function PostMediaGallery({
  images,
  videos,
  autoPlayVideo = false,
  className,
  onImageError,
  borderless = false
}: PostMediaGalleryProps) {
  const [imageLoadStates, setImageLoadStates] = useState<Record<string, 'loading' | 'loaded' | 'error'>>({});
  const [imageDimensions, setImageDimensions] = useState<Record<string, ImageDimensions>>({});

  const allMedia = [
    ...(images || []).map(src => ({ type: 'image' as const, src })),
    ...(videos || []).map(src => ({ type: 'video' as const, src }))
  ];

  const handleImageLoad = useCallback((src: string) => {
    setImageLoadStates(prev => ({ ...prev, [src]: 'loaded' }));

    // Load image dimensions for smart sizing
    if (!imageDimensions[src]) {
      loadImageDimensions(src)
        .then(dimensions => {
          setImageDimensions(prev => ({ ...prev, [src]: dimensions }));
        })
        .catch(error => {
          console.warn('Failed to load image dimensions:', error);
        });
    }
  }, [imageDimensions]);

  const handleImageLoadError = useCallback((src: string) => {
    setImageLoadStates(prev => ({ ...prev, [src]: 'error' }));
    onImageError?.(src);
  }, [onImageError]);

  if (!allMedia.length) return null;

  // Helper function to get image classes based on dimensions
  const getImageClasses = (dimensions: ImageDimensions | undefined) => {
    if (!dimensions) return "w-full object-cover";
    return getImageDisplayClasses(dimensions);
  };

  const renderMedia = (media: { type: 'image' | 'video'; src: string }, index: number, isInGrid: boolean = false) => {
    const loadState = imageLoadStates[media.src] || 'loading';
    const dimensions = imageDimensions[media.src];

    if (media.type === 'video') {
      return (
        <div key={media.src} className="relative w-full h-full min-h-[200px] bg-gray-100 overflow-hidden">
          <video
            src={media.src}
            controls
            autoPlay={autoPlayVideo}
            muted={autoPlayVideo}
            className="w-full h-full object-cover"
            onError={() => handleImageLoadError(media.src)}
          />
          <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
            Video
          </div>
        </div>
      );
    }

    // For single image, use smart sizing based on dimensions
    if (!isInGrid && dimensions) {
      const shouldCrop = shouldShowSeeMoreIndicator(dimensions);
      const containerClass = shouldCrop
        ? "relative w-full max-h-[1020px] bg-gray-100 overflow-hidden"
        : "relative w-full bg-gray-100 overflow-hidden";

      // For non-cropped images, calculate natural height
      const shouldUseFill = dimensions.type === 'portrait' || shouldCrop;

      return (
        <div key={media.src} className={containerClass}>
          {loadState === 'error' ? (
            <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-500 min-h-[200px]">
              <div className="text-center">
                <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="text-sm">Failed to load image</p>
              </div>
            </div>
          ) : shouldUseFill ? (
            <OptimizedImage
              src={media.src}
              alt={`Post image ${index + 1}`}
              fill
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
              className={getImageClasses(dimensions)}
              loadingClassName="bg-gray-200 animate-pulse"
              onLoad={() => handleImageLoad(media.src)}
              onError={() => handleImageLoadError(media.src)}
            />
          ) : (
            <OptimizedImage
              src={media.src}
              alt={`Post image ${index + 1}`}
              width={dimensions.type === 'square' ? 600 : dimensions.type === 'landscape' ? 800 : 600}
              height={dimensions.type === 'square' ? 600 : dimensions.type === 'landscape' ? 450 : 600}
              className={getImageClasses(dimensions)}
              loadingClassName="bg-gray-200 animate-pulse"
              onLoad={() => handleImageLoad(media.src)}
              onError={() => handleImageLoadError(media.src)}
            />
          )}
          
          {/* See more indicator for cropped images */}
          {shouldCrop && (
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent p-4">
              <div className="text-white text-sm font-medium">See more</div>
            </div>
          )}
        </div>
      );
    }

    // For grid images, always use fill with aspect ratio
    return (
      <div
        key={media.src}
        className="relative w-full h-full min-h-[200px] bg-gray-100 overflow-hidden"
      >
        {loadState === 'error' ? (
          <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-500">
            <div className="text-center">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="text-sm">Failed to load image</p>
            </div>
          </div>
        ) : (
          <OptimizedImage
            src={media.src}
            alt={`Post image ${index + 1}`}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
            className="w-full h-full object-cover"
            loadingClassName="bg-gray-200 animate-pulse"
            onLoad={() => handleImageLoad(media.src)}
            onError={() => handleImageLoadError(media.src)}
          />
        )}
      </div>
    );
  };

  const renderGallery = () => {
    const mediaCount = allMedia.length;
    const gapClass = borderless ? "gap-1" : "gap-2";

    if (mediaCount === 1) {
      return (
        <div className="w-full">
          {renderMedia(allMedia[0], 0, false)}
        </div>
      );
    }

    if (mediaCount === 2) {
      return (
        <div className={cn("grid grid-cols-2", gapClass)}>
          {allMedia.map((media, index) => renderMedia(media, index, true))}
        </div>
      );
    }

    if (mediaCount === 3) {
      return (
        <div className={cn("grid grid-cols-2", gapClass)}>
          <div className="row-span-2">
            {renderMedia(allMedia[0], 0, true)}
          </div>
          <div className="grid grid-rows-2 gap-2">
            {renderMedia(allMedia[1], 1, true)}
            {renderMedia(allMedia[2], 2, true)}
          </div>
        </div>
      );
    }

    // For 4+ images, show first 3 and a "+X more" overlay on the 4th
    return (
      <div className={cn("grid grid-cols-2", gapClass)}>
        {allMedia.slice(0, 3).map((media, index) => renderMedia(media, index, true))}
        <div className="relative">
          {renderMedia(allMedia[3], 3, true)}
          {allMedia.length > 4 && (
            <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
              <span className="text-white text-xl font-bold">
                +{allMedia.length - 4} more
              </span>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={cn(
      borderless ? "" : "mt-3",
      borderless ? "w-full -my-1" : "",
      className
    )}>
      {renderGallery()}
    </div>
  );
});
