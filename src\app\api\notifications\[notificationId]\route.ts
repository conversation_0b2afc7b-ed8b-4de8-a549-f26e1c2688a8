import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { notifications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { getSupabaseServiceClient } from "@/lib/supabase/client";

export async function PATCH(
  req: Request,
  context: { params: Promise<{ notificationId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get notification ID from params
    const params = await context.params;
    const { notificationId } = params;

    // Find the notification
    const notification = await db.query.notifications.findFirst({
      where: eq(notifications.id, notificationId),
    });

    if (!notification) {
      return NextResponse.json(
        { message: "Notification not found" },
        { status: 404 }
      );
    }

    // Check if the notification belongs to the current user
    if (notification.recipientId !== session.user.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 403 }
      );
    }

    // Mark the notification as read
    await db
      .update(notifications)
      .set({ read: true })
      .where(eq(notifications.id, notificationId));

    // Update real-time read status
    try {
      const supabase = getSupabaseServiceClient();
      await supabase
        .from('notifications_realtime')
        .update({ read_at: new Date().toISOString() })
        .eq('mysql_notification_id', notificationId);
    } catch (realtimeError) {
      console.error('Real-time notification read status update failed:', realtimeError);
      // Don't fail the request if real-time fails
    }

    return NextResponse.json(
      { message: "Notification marked as read" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error marking notification as read:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  context: { params: Promise<{ notificationId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get notification ID from params
    const params = await context.params;
    const { notificationId } = params;

    // Find the notification
    const notification = await db.query.notifications.findFirst({
      where: eq(notifications.id, notificationId),
    });

    if (!notification) {
      return NextResponse.json(
        { message: "Notification not found" },
        { status: 404 }
      );
    }

    // Check if the notification belongs to the current user
    if (notification.recipientId !== session.user.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 403 }
      );
    }

    // Delete the notification
    await db
      .delete(notifications)
      .where(eq(notifications.id, notificationId));

    return NextResponse.json(
      { message: "Notification deleted" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting notification:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
