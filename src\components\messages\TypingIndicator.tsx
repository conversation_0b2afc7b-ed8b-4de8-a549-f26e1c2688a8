"use client";

import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface TypingUser {
  id: string;
  name: string;
  image?: string;
}

interface TypingIndicatorProps {
  typingUsers: TypingUser[];
  className?: string;
  showAvatars?: boolean;
  maxUsers?: number;
}

export function TypingIndicator({
  typingUsers,
  className,
  showAvatars = true,
  maxUsers = 3,
}: TypingIndicatorProps) {
  const [dots, setDots] = useState("");

  // Animate typing dots
  useEffect(() => {
    if (typingUsers.length === 0) return;

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === "...") return "";
        return prev + ".";
      });
    }, 500);

    return () => clearInterval(interval);
  }, [typingUsers.length]);

  if (typingUsers.length === 0) return null;

  const displayUsers = typingUsers.slice(0, maxUsers);
  const remainingCount = typingUsers.length - maxUsers;

  const getTypingText = () => {
    if (typingUsers.length === 1) {
      return `${displayUsers[0].name} is typing`;
    } else if (typingUsers.length === 2) {
      return `${displayUsers[0].name} and ${displayUsers[1].name} are typing`;
    } else if (typingUsers.length <= maxUsers) {
      const names = displayUsers.slice(0, -1).map(u => u.name).join(", ");
      return `${names} and ${displayUsers[displayUsers.length - 1].name} are typing`;
    } else {
      const names = displayUsers.map(u => u.name).join(", ");
      return `${names} and ${remainingCount} other${remainingCount > 1 ? 's' : ''} are typing`;
    }
  };

  return (
    <div className={cn(
      "flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border-l-4 border-blue-400",
      "animate-pulse",
      className
    )}>
      {/* User avatars */}
      {showAvatars && (
        <div className="flex -space-x-2">
          {displayUsers.map((user, index) => (
            <div
              key={user.id}
              className="relative"
              style={{ zIndex: displayUsers.length - index }}
            >
              {user.image ? (
                <Image
                  src={user.image}
                  alt={user.name}
                  width={24}
                  height={24}
                  className="h-6 w-6 rounded-full border-2 border-white bg-gray-200"
                />
              ) : (
                <div className="h-6 w-6 rounded-full border-2 border-white bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-xs font-semibold text-white">
                    {user.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
          ))}
          {remainingCount > 0 && (
            <div className="h-6 w-6 rounded-full border-2 border-white bg-gray-400 flex items-center justify-center">
              <span className="text-xs font-semibold text-white">
                +{remainingCount}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Typing text */}
      <div className="flex items-center space-x-1">
        <span className="text-sm text-gray-600">
          {getTypingText()}
        </span>
        <span className="text-blue-500 font-bold min-w-[20px]">
          {dots}
        </span>
      </div>

      {/* Animated dots */}
      <div className="flex space-x-1">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={cn(
              "h-2 w-2 bg-blue-400 rounded-full animate-bounce",
              `animation-delay-${i * 200}`
            )}
            style={{
              animationDelay: `${i * 0.2}s`,
              animationDuration: '1.4s',
            }}
          />
        ))}
      </div>
    </div>
  );
}

// Simple typing indicator for single conversation
interface SimpleTypingIndicatorProps {
  isTyping: boolean;
  userName?: string;
  className?: string;
}

export function SimpleTypingIndicator({
  isTyping,
  userName = "Someone",
  className,
}: SimpleTypingIndicatorProps) {
  const [dots, setDots] = useState("");

  useEffect(() => {
    if (!isTyping) return;

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === "...") return "";
        return prev + ".";
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isTyping]);

  if (!isTyping) return null;

  return (
    <div className={cn(
      "flex items-center space-x-2 text-sm text-gray-500 italic",
      className
    )}>
      <span>{userName} is typing</span>
      <span className="text-blue-500 font-bold min-w-[20px]">{dots}</span>
    </div>
  );
}

// Typing indicator for message bubbles
export function BubbleTypingIndicator({ className }: { className?: string }) {
  return (
    <div className={cn(
      "flex items-center space-x-1 p-3 bg-gray-200 rounded-2xl rounded-bl-md max-w-[60px]",
      className
    )}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className="h-2 w-2 bg-gray-500 rounded-full animate-bounce"
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1.4s',
          }}
        />
      ))}
    </div>
  );
}
