/**
 * User Presence Component
 * Shows online/offline status and last seen information
 */

"use client";

import { useEffect, useState } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { useRealtimeNotifications } from '@/hooks/useRealtimeNotifications';
import { cn } from '@/lib/utils';

interface UserPresenceProps {
  userId: string;
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function UserPresence({ 
  userId, 
  className, 
  showText = false, 
  size = 'md' 
}: UserPresenceProps) {
  const [status, setStatus] = useState<'online' | 'offline' | 'away'>('offline');
  const [lastSeen, setLastSeen] = useState<string | null>(null);

  const { getUserOnlineStatus, getUserLastSeen, isConnected } = useRealtimeNotifications({
    watchUsers: [userId],
    onPresenceUpdate: (users) => {
      const user = users.find(u => u.userId === userId);
      if (user) {
        setStatus(user.status);
        setLastSeen(user.lastSeen);
      }
    },
  });

  // Update status when component mounts or userId changes
  useEffect(() => {
    if (isConnected) {
      const currentStatus = getUserOnlineStatus(userId);
      const currentLastSeen = getUserLastSeen(userId);
      setStatus(currentStatus);
      setLastSeen(currentLastSeen);
    }
  }, [userId, isConnected, getUserOnlineStatus, getUserLastSeen]);

  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  const statusColors = {
    online: 'bg-green-500',
    away: 'bg-yellow-500',
    offline: 'bg-gray-400',
  };

  const getStatusText = () => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'away':
        return 'Away';
      case 'offline':
        if (lastSeen) {
          try {
            return `Last seen ${formatDistanceToNow(new Date(lastSeen), { addSuffix: true })}`;
          } catch {
            return 'Offline';
          }
        }
        return 'Offline';
      default:
        return 'Offline';
    }
  };

  if (!isConnected) {
    return null; // Don't show presence if not connected to real-time
  }

  return (
    <div className={cn('flex items-center', className)}>
      <div className="relative">
        <div
          className={cn(
            'rounded-full border-2 border-white',
            sizeClasses[size],
            statusColors[status]
          )}
        />
        {status === 'online' && (
          <div
            className={cn(
              'absolute inset-0 rounded-full animate-ping',
              statusColors[status],
              'opacity-75'
            )}
          />
        )}
      </div>
      
      {showText && (
        <span className="ml-2 text-sm text-gray-600">
          {getStatusText()}
        </span>
      )}
    </div>
  );
}

/**
 * User Avatar with Presence
 * Combines user avatar with presence indicator
 */
interface UserAvatarWithPresenceProps {
  userId: string;
  userImage?: string | null;
  userName?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showPresence?: boolean;
}

export function UserAvatarWithPresence({
  userId,
  userImage,
  userName,
  size = 'md',
  className,
  showPresence = true,
}: UserAvatarWithPresenceProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
  };

  const presenceSizes = {
    sm: 'sm' as const,
    md: 'sm' as const,
    lg: 'md' as const,
    xl: 'md' as const,
  };

  return (
    <div className={cn('relative', className)}>
      {userImage ? (
        <img
          src={userImage}
          alt={userName || 'User'}
          className={cn(
            'rounded-full object-cover',
            sizeClasses[size]
          )}
        />
      ) : (
        <div
          className={cn(
            'bg-gray-300 rounded-full flex items-center justify-center',
            sizeClasses[size]
          )}
        >
          <span className="text-gray-600 text-sm font-medium">
            {userName?.charAt(0)?.toUpperCase() || '?'}
          </span>
        </div>
      )}
      
      {showPresence && (
        <div className="absolute -bottom-1 -right-1">
          <UserPresence
            userId={userId}
            size={presenceSizes[size]}
          />
        </div>
      )}
    </div>
  );
}

/**
 * Online Users List
 * Shows a list of currently online users
 */
interface OnlineUsersListProps {
  userIds: string[];
  className?: string;
  maxDisplay?: number;
}

export function OnlineUsersList({ 
  userIds, 
  className, 
  maxDisplay = 5 
}: OnlineUsersListProps) {
  const [onlineUsers, setOnlineUsers] = useState<any[]>([]);

  const { onlineUsers: realtimeUsers } = useRealtimeNotifications({
    watchUsers: userIds,
    onPresenceUpdate: (users) => {
      const online = users.filter(u => u.status === 'online');
      setOnlineUsers(online);
    },
  });

  const displayUsers = onlineUsers.slice(0, maxDisplay);
  const remainingCount = Math.max(0, onlineUsers.length - maxDisplay);

  if (onlineUsers.length === 0) {
    return null;
  }

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <div className="flex -space-x-2">
        {displayUsers.map((user) => (
          <UserAvatarWithPresence
            key={user.userId}
            userId={user.userId}
            size="sm"
            className="ring-2 ring-white"
          />
        ))}
      </div>
      
      {remainingCount > 0 && (
        <span className="text-sm text-gray-500">
          +{remainingCount} more online
        </span>
      )}
      
      <div className="flex items-center space-x-1">
        <div className="w-2 h-2 bg-green-500 rounded-full" />
        <span className="text-sm text-gray-600">
          {onlineUsers.length} online
        </span>
      </div>
    </div>
  );
}
