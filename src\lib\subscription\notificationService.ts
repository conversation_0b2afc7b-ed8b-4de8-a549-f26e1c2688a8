import { db } from "@/lib/db";
import { notifications, users, subscriptionPlans } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export interface SubscriptionNotificationData {
  userId: string;
  type: 'subscription_created' | 'subscription_activated' | 'subscription_cancelled' | 'subscription_expired' | 'payment_failed' | 'payment_successful' | 'subscription_renewed';
  subscriptionId: string;
  planId: string;
  metadata?: any;
}

export class SubscriptionNotificationService {
  // Send notification for subscription events
  static async sendSubscriptionNotification(data: SubscriptionNotificationData): Promise<void> {
    try {
      // Get user and plan details
      const [user, plan] = await Promise.all([
        db.query.users.findFirst({
          where: eq(users.id, data.userId),
        }),
        db.query.subscriptionPlans.findFirst({
          where: eq(subscriptionPlans.id, data.planId),
        }),
      ]);

      if (!user || !plan) {
        console.error('User or plan not found for notification:', data);
        return;
      }

      // Generate notification content based on type
      const notificationContent = this.generateNotificationContent(data.type, plan.displayName, data.metadata);

      // Create notification record
      const notificationId = uuidv4();
      await db.insert(notifications).values({
        id: notificationId,
        recipientId: data.userId,
        type: 'subscription',
        title: notificationContent.title,
        message: notificationContent.message,
        metadata: JSON.stringify({
          subscriptionId: data.subscriptionId,
          planId: data.planId,
          subscriptionEventType: data.type,
          ...data.metadata,
        }),
        isRead: false,
      });

      // Send email notification (if enabled)
      await this.sendEmailNotification(user.email, notificationContent, data);

      // Send push notification (if enabled)
      await this.sendPushNotification(data.userId, notificationContent, data);

      console.log(`Subscription notification sent to user ${data.userId} for event ${data.type}`);
    } catch (error) {
      console.error('Error sending subscription notification:', error);
    }
  }

  // Generate notification content based on event type
  private static generateNotificationContent(
    type: SubscriptionNotificationData['type'],
    planName: string,
    metadata?: any
  ): { title: string; message: string } {
    switch (type) {
      case 'subscription_created':
        return {
          title: 'Subscription Created',
          message: `Your ${planName} subscription has been created and is pending activation.`,
        };

      case 'subscription_activated':
        return {
          title: 'Subscription Activated',
          message: `Welcome! Your ${planName} subscription is now active. Enjoy all the premium features.`,
        };

      case 'subscription_cancelled':
        return {
          title: 'Subscription Cancelled',
          message: `Your ${planName} subscription has been cancelled. You'll continue to have access until ${metadata?.endDate || 'the end of your billing period'}.`,
        };

      case 'subscription_expired':
        return {
          title: 'Subscription Expired',
          message: `Your ${planName} subscription has expired. Renew now to continue enjoying premium features.`,
        };

      case 'payment_failed':
        return {
          title: 'Payment Failed',
          message: `We couldn't process your payment for ${planName}. Please update your payment method to avoid service interruption.`,
        };

      case 'payment_successful':
        return {
          title: 'Payment Successful',
          message: `Thank you! Your payment for ${planName} has been processed successfully.`,
        };

      case 'subscription_renewed':
        return {
          title: 'Subscription Renewed',
          message: `Your ${planName} subscription has been automatically renewed. Thank you for your continued support!`,
        };

      default:
        return {
          title: 'Subscription Update',
          message: `There's an update regarding your ${planName} subscription.`,
        };
    }
  }

  // Send email notification (placeholder - integrate with your email service)
  private static async sendEmailNotification(
    email: string,
    content: { title: string; message: string },
    data: SubscriptionNotificationData
  ): Promise<void> {
    try {
      // This is a placeholder for email integration
      // You would integrate with services like SendGrid, AWS SES, etc.
      
      console.log(`Email notification would be sent to ${email}:`, {
        subject: content.title,
        body: content.message,
        subscriptionEvent: data.type,
      });

      // Example integration:
      /*
      await emailService.send({
        to: email,
        subject: content.title,
        template: 'subscription-notification',
        data: {
          title: content.title,
          message: content.message,
          subscriptionId: data.subscriptionId,
          eventType: data.type,
        },
      });
      */
    } catch (error) {
      console.error('Error sending email notification:', error);
    }
  }

  // Send push notification (placeholder - integrate with your push service)
  private static async sendPushNotification(
    userId: string,
    content: { title: string; message: string },
    data: SubscriptionNotificationData
  ): Promise<void> {
    try {
      // This is a placeholder for push notification integration
      // You would integrate with services like Firebase Cloud Messaging, OneSignal, etc.
      
      console.log(`Push notification would be sent to user ${userId}:`, {
        title: content.title,
        body: content.message,
        subscriptionEvent: data.type,
      });

      // Example integration:
      /*
      await pushService.send({
        userId: userId,
        title: content.title,
        body: content.message,
        data: {
          subscriptionId: data.subscriptionId,
          eventType: data.type,
        },
      });
      */
    } catch (error) {
      console.error('Error sending push notification:', error);
    }
  }

  // Send bulk notifications to multiple users
  static async sendBulkNotifications(
    userIds: string[],
    notificationData: Omit<SubscriptionNotificationData, 'userId'>
  ): Promise<void> {
    try {
      const promises = userIds.map(userId =>
        this.sendSubscriptionNotification({
          ...notificationData,
          userId,
        })
      );

      await Promise.allSettled(promises);
      console.log(`Bulk notifications sent to ${userIds.length} users`);
    } catch (error) {
      console.error('Error sending bulk notifications:', error);
    }
  }

  // Send admin notification for subscription events
  static async sendAdminNotification(
    eventType: string,
    subscriptionId: string,
    userId: string,
    metadata?: any
  ): Promise<void> {
    try {
      // Get admin users (you might want to have a specific admin notification table)
      const adminUsers = await db.query.users.findMany({
        where: eq(users.isAdmin, true),
      });

      if (adminUsers.length === 0) {
        return;
      }

      const notificationContent = this.generateAdminNotificationContent(eventType, metadata);

      // Send notification to all admin users
      const promises = adminUsers.map(async (admin) => {
        const notificationId = uuidv4();
        return db.insert(notifications).values({
          id: notificationId,
          recipientId: admin.id,
          type: 'admin_subscription',
          title: notificationContent.title,
          message: notificationContent.message,
          metadata: JSON.stringify({
            subscriptionId,
            userId,
            eventType,
            ...metadata,
          }),
          isRead: false,
        });
      });

      await Promise.all(promises);
      console.log(`Admin notifications sent for subscription event: ${eventType}`);
    } catch (error) {
      console.error('Error sending admin notification:', error);
    }
  }

  // Generate admin notification content
  private static generateAdminNotificationContent(
    eventType: string,
    metadata?: any
  ): { title: string; message: string } {
    switch (eventType) {
      case 'subscription_created':
        return {
          title: 'New Subscription',
          message: `A new subscription has been created and requires attention.`,
        };

      case 'payment_failed':
        return {
          title: 'Payment Failed',
          message: `A subscription payment has failed and may require manual intervention.`,
        };

      case 'subscription_cancelled':
        return {
          title: 'Subscription Cancelled',
          message: `A user has cancelled their subscription.`,
        };

      case 'chargeback':
        return {
          title: 'Chargeback Alert',
          message: `A chargeback has been initiated for a subscription payment.`,
        };

      default:
        return {
          title: 'Subscription Event',
          message: `A subscription event requires admin attention: ${eventType}`,
        };
    }
  }
}
