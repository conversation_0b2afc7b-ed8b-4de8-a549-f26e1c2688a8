"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { UserIcon, UserPlusIcon } from "@heroicons/react/24/outline";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { toast } from "react-hot-toast";

interface UserSuggestion {
  id: string;
  name: string;
  username: string;
  image: string | null;
  bio: string | null;
  location: string | null;
  mutualFriendsCount: number;
  createdAt: string;
}

interface PeopleSuggestionsPreviewProps {
  showAll?: boolean;
  limit?: number;
  className?: string;
}

export function PeopleSuggestionsPreview({ 
  showAll = false, 
  limit,
  className = ""
}: PeopleSuggestionsPreviewProps) {
  const { data: session } = useSession();
  const [suggestions, setSuggestions] = useState<UserSuggestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [sendingRequests, setSendingRequests] = useState<Set<string>>(new Set());

  // Determine the limit based on showAll prop
  const fetchLimit = limit || (showAll ? 20 : 3);

  useEffect(() => {
    if (session?.user) {
      fetchSuggestions();
    }
  }, [session, fetchLimit]);

  const fetchSuggestions = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/users/suggestions?limit=${fetchLimit}`);

      if (!response.ok) {
        throw new Error('Failed to fetch suggestions');
      }

      const data = await response.json();
      setSuggestions(data.suggestions || []);
    } catch (error) {
      console.error('Error fetching suggestions:', error);
      // Don't show error toast for suggestions as it's not critical
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribeToUser = async (userId: string) => {
    if (sendingRequests.has(userId)) return;

    try {
      setSendingRequests(prev => new Set(prev).add(userId));

      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ targetUserId: userId }),
      });

      const data = await response.json();

      if (response.ok) {
        // Remove the user from suggestions
        setSuggestions(prev => prev.filter(user => user.id !== userId));
        toast.success('Subscribed successfully!');
      } else {
        toast.error(data.message || 'Failed to subscribe');
      }
    } catch (error) {
      console.error('Error subscribing to user:', error);
      toast.error('Failed to subscribe');
    } finally {
      setSendingRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  if (loading) {
    const skeletonCount = showAll ? 6 : 3;
    return (
      <div className={`${showAll ? '' : 'rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300'} ${className}`}>
        {!showAll && (
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-base font-semibold text-gray-900">
              People You May Know
            </h2>
          </div>
        )}
        <div className={showAll ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "space-y-3"}>
          {Array.from({ length: skeletonCount }).map((_, i) => (
            <div key={i} className={`${showAll ? 'p-4 border border-gray-100 rounded-lg' : 'p-1 rounded-lg'} animate-pulse`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                  <div className="ml-3">
                    <div className="h-4 bg-gray-200 rounded w-20 mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
                <div className="h-7 w-16 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (suggestions.length === 0) {
    if (showAll) {
      return (
        <div className="text-center py-12">
          <UserIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No suggestions available</h3>
          <p className="text-gray-600">Check back later for new people to connect with.</p>
        </div>
      );
    }
    return null; // Don't show the section if there are no suggestions
  }

  if (showAll) {
    return (
      <div className={className}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {suggestions.map((user) => (
            <div key={user.id} className="p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow duration-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center flex-1 min-w-0">
                  <div className="relative h-12 w-12 flex-shrink-0 rounded-full bg-gradient-to-r from-blue-100 to-indigo-100 flex items-center justify-center shadow-sm">
                    {user.image ? (
                      <OptimizedImage
                        src={user.image}
                        alt={user.name}
                        width={48}
                        height={48}
                        className="rounded-full object-cover"
                      />
                    ) : (
                      <UserIcon className="h-6 w-6 text-gray-500" />
                    )}
                  </div>
                  <div className="ml-3 flex-1 min-w-0">
                    <Link
                      href={`/user/${user.username}`}
                      className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors duration-200 block truncate"
                    >
                      {user.name}
                    </Link>
                    <p className="text-xs text-gray-500 truncate">
                      @{user.username}
                    </p>
                    {user.mutualFriendsCount > 0 ? (
                      <p className="text-xs text-gray-500 truncate mt-1">
                        {user.mutualFriendsCount} mutual connection{user.mutualFriendsCount !== 1 ? 's' : ''}
                      </p>
                    ) : user.location ? (
                      <p className="text-xs text-gray-500 truncate mt-1">
                        {user.location}
                      </p>
                    ) : (
                      <p className="text-xs text-gray-500 mt-1">
                        New to HIFNF
                      </p>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => handleSubscribeToUser(user.id)}
                  disabled={sendingRequests.has(user.id)}
                  className="ml-2 flex items-center justify-center px-3 py-1.5 text-xs font-medium text-white bg-blue-500 rounded-md hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
                  title="Subscribe"
                >
                  {sendingRequests.has(user.id) ? (
                    <div className="h-3 w-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <>
                      <UserPlusIcon className="h-3 w-3 mr-1" />
                      Subscribe
                    </>
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-base font-semibold text-gray-900">
          People You May Know
        </h2>
        <Link href="/connection?tab=suggestions" className="text-sm text-blue-500 hover:text-blue-600 hover:underline transition-colors duration-200">
          See All
        </Link>
      </div>
      <div className="space-y-3">
        {suggestions.map((user) => (
          <div key={user.id} className="flex items-center justify-between p-1 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            <div className="flex items-center flex-1 min-w-0">
              <div className="relative h-10 w-10 flex-shrink-0 rounded-full bg-gradient-to-r from-blue-100 to-indigo-100 flex items-center justify-center shadow-sm">
                {user.image ? (
                  <OptimizedImage
                    src={user.image}
                    alt={user.name}
                    width={40}
                    height={40}
                    className="rounded-full object-cover"
                  />
                ) : (
                  <UserIcon className="h-6 w-6 text-gray-500" />
                )}
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <Link
                  href={`/user/${user.username}`}
                  className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors duration-200 block truncate"
                >
                  {user.name}
                </Link>
                {user.mutualFriendsCount > 0 ? (
                  <p className="text-xs text-gray-500 truncate">
                    {user.mutualFriendsCount} mutual connection{user.mutualFriendsCount !== 1 ? 's' : ''}
                  </p>
                ) : user.location ? (
                  <p className="text-xs text-gray-500 truncate">
                    {user.location}
                  </p>
                ) : (
                  <p className="text-xs text-gray-500">
                    New to HIFNF
                  </p>
                )}
              </div>
            </div>
            <button
              onClick={() => handleSubscribeToUser(user.id)}
              disabled={sendingRequests.has(user.id)}
              className="ml-2 flex items-center justify-center h-7 w-7 rounded-full bg-blue-500 text-white hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
              title="Subscribe"
            >
              {sendingRequests.has(user.id) ? (
                <div className="h-3 w-3 border border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <UserPlusIcon className="h-4 w-4" />
              )}
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
