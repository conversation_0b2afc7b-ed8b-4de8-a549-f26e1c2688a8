/**
 * Data Sync Script
 * Manually trigger sync operations between MySQL and Supabase
 */

import { hybridSyncService } from '../lib/services/hybridSyncService';

async function runSync() {
  console.log('🚀 Starting manual data sync...');
  console.log('=====================================');

  try {
    // Health check first
    console.log('🔍 Performing health check...');
    const health = await hybridSyncService.healthCheck();
    
    if (!health.healthy) {
      console.log('⚠️ Health check issues detected:');
      health.issues.forEach(issue => console.log(`  - ${issue}`));
      console.log('');
    } else {
      console.log('✅ System health check passed');
    }

    // Perform full sync
    console.log('🔄 Performing full sync...');
    const stats = await hybridSyncService.performFullSync();

    console.log('');
    console.log('📊 Sync Results:');
    console.log('=====================================');
    console.log(`📩 Messages synced: ${stats.messagesSynced}`);
    console.log(`🔔 Notifications synced: ${stats.notificationsSynced}`);
    console.log(`🗑️ Messages cleaned up: ${stats.messagesCleanedUp}`);
    console.log(`🗑️ Notifications cleaned up: ${stats.notificationsCleanedUp}`);
    
    if (stats.errors.length > 0) {
      console.log('');
      console.log('❌ Errors encountered:');
      stats.errors.forEach(error => console.log(`  - ${error}`));
    }

    console.log('');
    console.log('✅ Manual sync completed successfully!');

  } catch (error) {
    console.error('❌ Manual sync failed:', error);
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const operation = args[0] || 'full';
const hoursBack = parseInt(args[1]) || 24;

async function runSpecificOperation() {
  console.log(`🚀 Running ${operation} operation...`);
  
  try {
    let result;

    switch (operation) {
      case 'messages':
        console.log(`📩 Syncing messages from last ${hoursBack} hours...`);
        result = await hybridSyncService.syncRecentMessages(hoursBack);
        console.log(`✅ Synced ${result.synced} messages`);
        if (result.errors.length > 0) {
          console.log('❌ Errors:', result.errors);
        }
        break;

      case 'notifications':
        console.log(`🔔 Syncing notifications from last ${hoursBack} hours...`);
        result = await hybridSyncService.syncRecentNotifications(hoursBack);
        console.log(`✅ Synced ${result.synced} notifications`);
        if (result.errors.length > 0) {
          console.log('❌ Errors:', result.errors);
        }
        break;

      case 'cleanup':
        console.log('🧹 Cleaning up old data...');
        result = await hybridSyncService.cleanupOldData();
        console.log(`✅ Deleted ${result.messagesDeleted} old messages and ${result.notificationsDeleted} old notifications`);
        if (result.errors.length > 0) {
          console.log('❌ Errors:', result.errors);
        }
        break;

      case 'health':
        console.log('🔍 Performing health check...');
        const health = await hybridSyncService.healthCheck();
        if (health.healthy) {
          console.log('✅ System is healthy');
        } else {
          console.log('⚠️ Health issues detected:');
          health.issues.forEach(issue => console.log(`  - ${issue}`));
        }
        break;

      case 'full':
      default:
        await runSync();
        return;
    }

    console.log('✅ Operation completed successfully!');

  } catch (error) {
    console.error(`❌ ${operation} operation failed:`, error);
    process.exit(1);
  }
}

// Show usage if help is requested
if (args.includes('--help') || args.includes('-h')) {
  console.log('📖 Data Sync Script Usage:');
  console.log('=====================================');
  console.log('npm run sync-data [operation] [hoursBack]');
  console.log('');
  console.log('Operations:');
  console.log('  full         - Full sync (default)');
  console.log('  messages     - Sync messages only');
  console.log('  notifications - Sync notifications only');
  console.log('  cleanup      - Cleanup old data only');
  console.log('  health       - Health check only');
  console.log('');
  console.log('Examples:');
  console.log('  npm run sync-data');
  console.log('  npm run sync-data messages 12');
  console.log('  npm run sync-data cleanup');
  console.log('  npm run sync-data health');
  process.exit(0);
}

// Run the operation
runSpecificOperation();
