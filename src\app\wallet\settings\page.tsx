import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { WalletSettings } from "@/components/wallet/WalletSettings";

export const dynamic = 'force-dynamic';

export default async function WalletSettingsPage() {
  const user = await requireAuth();

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center space-x-4">
            <a
              href="/wallet"
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </a>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Wallet Settings</h1>
              <p className="text-gray-600 mt-2">
                Manage your wallet security, payment methods, and preferences
              </p>
            </div>
          </div>
        </div>

        <WalletSettings userId={user.id} />
      </div>
    </MainLayout>
  );
}
