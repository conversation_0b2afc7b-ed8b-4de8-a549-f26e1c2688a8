"use client";

import { ReactNode, useEffect } from "react";
import { Navbar } from "./Navbar";
import { useSession } from "next-auth/react";
import { FanPageMessageProvider } from "@/contexts/FanPageMessageContext";
import { FanPageMessageBox } from "@/components/fan-pages/FanPageMessageBox";
import { useRealtimeNotifications } from "@/hooks/useRealtimeNotifications";

interface MainLayoutProps {
  children: ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const { status, data: session } = useSession();
  const isAuthenticated = status === "authenticated";

  // Initialize real-time notifications for authenticated users
  const { updatePresence } = useRealtimeNotifications();

  // Handle page visibility changes for presence updates
  useEffect(() => {
    if (!isAuthenticated) return;

    const handleVisibilityChange = () => {
      if (document.hidden) {
        updatePresence('away');
      } else {
        updatePresence('online');
      }
    };

    const handleBeforeUnload = () => {
      updatePresence('offline');
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isAuthenticated, updatePresence]);

  return (
    <FanPageMessageProvider>
      <div className="flex min-h-screen flex-col bg-gray-50">
        {isAuthenticated && <Navbar />}
        <main className="flex-1 pt-16">{children}</main>
        {isAuthenticated && <FanPageMessageBox />}
      </div>
    </FanPageMessageProvider>
  );
}
