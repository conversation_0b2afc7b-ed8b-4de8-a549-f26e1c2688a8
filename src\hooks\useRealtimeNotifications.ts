/**
 * Real-time Notifications Hook
 * Custom hook for managing real-time notifications and user presence
 */

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'react-hot-toast';
import { 
  realtimeNotificationService, 
  NotificationEvent, 
  OnlineUser 
} from '@/lib/services/realtimeNotificationService';
import { 
  getNotificationDisplayText, 
  getNotificationIcon, 
  shouldShowBrowserNotification 
} from '@/lib/utils/notificationHelpers';

export interface UseRealtimeNotificationsProps {
  onNewNotification?: (notification: any) => void;
  onNotificationRead?: (notificationId: string) => void;
  onPresenceUpdate?: (users: OnlineUser[]) => void;
  watchUsers?: string[]; // User IDs to watch for presence
}

export function useRealtimeNotifications({
  onNewNotification,
  onNotificationRead,
  onPresenceUpdate,
  watchUsers = [],
}: UseRealtimeNotificationsProps = {}) {
  const { data: session } = useSession();
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Initialize real-time service
  useEffect(() => {
    if (session?.user?.id) {
      realtimeNotificationService.initialize(session.user.id);
      setIsConnected(true);

      // Handle page visibility changes
      const handleVisibilityChange = () => {
        realtimeNotificationService.handleVisibilityChange();
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        realtimeNotificationService.cleanup();
        setIsConnected(false);
      };
    }
  }, [session?.user?.id]);

  // Subscribe to notifications
  useEffect(() => {
    if (session?.user?.id && isConnected) {
      const handleNotificationEvent = (event: NotificationEvent) => {
        switch (event.type) {
          case 'new_notification':
            const notification = event.data;
            
            // Update unread count
            setUnreadCount(prev => prev + 1);
            
            // Show toast notification
            const displayText = getNotificationDisplayText(notification.type);
            const icon = getNotificationIcon(notification.type);
            
            toast.success(`${icon} ${displayText}`, {
              duration: 4000,
              position: 'top-right',
            });

            // Show browser notification if supported and enabled
            if (shouldShowBrowserNotification(notification.type)) {
              showBrowserNotification(notification);
            }

            // Call callback
            onNewNotification?.(notification);
            break;

          case 'notification_read':
            setUnreadCount(prev => Math.max(0, prev - 1));
            onNotificationRead?.(event.data.mysql_notification_id);
            break;
        }
      };

      realtimeNotificationService.subscribeToNotifications(handleNotificationEvent);

      return () => {
        realtimeNotificationService.unsubscribeFromNotifications();
      };
    }
  }, [session?.user?.id, isConnected, onNewNotification, onNotificationRead]);

  // Subscribe to user presence
  useEffect(() => {
    if (watchUsers.length > 0 && isConnected) {
      const handlePresenceUpdate = (users: OnlineUser[]) => {
        setOnlineUsers(users);
        onPresenceUpdate?.(users);
      };

      realtimeNotificationService.subscribeToPresence(watchUsers, handlePresenceUpdate);

      return () => {
        realtimeNotificationService.unsubscribeFromPresence();
      };
    }
  }, [watchUsers, isConnected, onPresenceUpdate]);

  // Show browser notification
  const showBrowserNotification = useCallback((notification: any) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      const displayText = getNotificationDisplayText(notification.type);
      const icon = getNotificationIcon(notification.type);
      
      new Notification(`${icon} New Notification`, {
        body: displayText,
        icon: '/favicon.ico',
        tag: notification.id,
      });
    }
  }, []);

  // Request notification permission
  const requestNotificationPermission = useCallback(async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }, []);

  // Send notification
  const sendNotification = useCallback(async (
    notificationId: string,
    recipientId: string,
    type: string,
    data: any
  ) => {
    return await realtimeNotificationService.sendRealtimeNotification(
      notificationId,
      recipientId,
      type,
      data
    );
  }, []);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    await realtimeNotificationService.markNotificationAsRead(notificationId);
    setUnreadCount(prev => Math.max(0, prev - 1));
  }, []);

  // Update presence
  const updatePresence = useCallback(async (status: 'online' | 'offline' | 'away') => {
    await realtimeNotificationService.updatePresence(status);
  }, []);

  // Check if user is online
  const isUserOnline = useCallback(async (userId: string) => {
    return await realtimeNotificationService.isUserOnline(userId);
  }, []);

  // Get online status for a user
  const getUserOnlineStatus = useCallback((userId: string) => {
    const user = onlineUsers.find(u => u.userId === userId);
    return user?.status || 'offline';
  }, [onlineUsers]);

  // Get last seen for a user
  const getUserLastSeen = useCallback((userId: string) => {
    const user = onlineUsers.find(u => u.userId === userId);
    return user?.lastSeen;
  }, [onlineUsers]);

  return {
    isConnected,
    onlineUsers,
    unreadCount,
    sendNotification,
    markAsRead,
    updatePresence,
    isUserOnline,
    getUserOnlineStatus,
    getUserLastSeen,
    requestNotificationPermission,
    setUnreadCount, // For manual updates
  };
}
