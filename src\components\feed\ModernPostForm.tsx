"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { useDropzone } from "react-dropzone";
import eventBus from "@/lib/eventBus";
import EmojiPicker from 'emoji-picker-react';
import { Dialog, DialogPanel, DialogTitle, Transition } from "@headlessui/react";
import { XMarkIcon, PhotoIcon, VideoCameraIcon, FaceSmileIcon, MapPinIcon, PaintBrushIcon, ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import { Fragment } from "react";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";
import { validateImageFiles, areAllValidationResultsValid, getValidationErrors } from "@/lib/utils/image-validation";


// Enhanced background color options with modern gradients and solid colors
const backgroundColors = [
  { name: "None", value: "", gradient: false, textColor: "#374151" },

  // Solid Colors
  { name: "Sky Blue", value: "#dbeafe", gradient: false, textColor: "#1e40af" },
  { name: "Emerald", value: "#d1fae5", gradient: false, textColor: "#065f46" },
  { name: "Amber", value: "#fef3c7", gradient: false, textColor: "#92400e" },
  { name: "Rose", value: "#fce7f3", gradient: false, textColor: "#be185d" },
  { name: "Violet", value: "#ede9fe", gradient: false, textColor: "#6b21a8" },
  { name: "Teal", value: "#ccfbf1", gradient: false, textColor: "#134e4a" },
  { name: "Orange", value: "#fed7aa", gradient: false, textColor: "#c2410c" },
  { name: "Indigo", value: "#e0e7ff", gradient: false, textColor: "#3730a3" },

  // Modern Gradients
  { name: "Ocean Blue", value: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", gradient: true, textColor: "#ffffff" },
  { name: "Sunset", value: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)", gradient: true, textColor: "#ffffff" },
  { name: "Forest", value: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", gradient: true, textColor: "#ffffff" },
  { name: "Purple Dream", value: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", gradient: true, textColor: "#374151" },
  { name: "Fire", value: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)", gradient: true, textColor: "#374151" },
  { name: "Northern Lights", value: "linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)", gradient: true, textColor: "#ffffff" },
  { name: "Golden Hour", value: "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", gradient: true, textColor: "#374151" },
  { name: "Mystic", value: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", gradient: true, textColor: "#ffffff" },
  { name: "Peachy", value: "linear-gradient(135deg, #ff758c 0%, #ff7eb3 100%)", gradient: true, textColor: "#ffffff" },
  { name: "Cool Blues", value: "linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)", gradient: true, textColor: "#ffffff" },
];

// Feeling options
const feelings = [
  { emoji: "😊", name: "Happy" },
  { emoji: "😢", name: "Sad" },
  { emoji: "😍", name: "Loved" },
  { emoji: "😡", name: "Angry" },
  { emoji: "🤔", name: "Thinking" },
  { emoji: "😴", name: "Sleepy" },
  { emoji: "🥳", name: "Celebrating" },
  { emoji: "😎", name: "Cool" },
  { emoji: "🤒", name: "Sick" },
];

// Activity options
const activities = [
  { emoji: "🍽️", name: "Eating" },
  { emoji: "🎮", name: "Playing" },
  { emoji: "📚", name: "Reading" },
  { emoji: "🎬", name: "Watching" },
  { emoji: "🏃", name: "Running" },
  { emoji: "✈️", name: "Traveling" },
  { emoji: "🎵", name: "Listening" },
  { emoji: "💼", name: "Working" },
  { emoji: "🎓", name: "Studying" },
];

// Privacy options
const privacyOptions = [
  {
    value: "public",
    label: "Public",
    icon: "🌎",
    description: "Anyone can see this post"
  },
  {
    value: "subscribers",
    label: "Subscribers",
    icon: "👥",
    description: "Only your subscribers can see this post"
  },
  {
    value: "private",
    label: "Only me",
    icon: "🔒",
    description: "Only you can see this post"
  },
];

export function ModernPostForm() {
  const { data: session } = useSession();
  const router = useRouter();
  const [content, setContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [images, setImages] = useState<File[]>([]);
  const [videos, setVideos] = useState<File[]>([]);
  const [privacy, setPrivacy] = useState<"public" | "subscribers" | "private">("public");
  const [backgroundColor, setBackgroundColor] = useState("");
  const [isGradient, setIsGradient] = useState(false);
  const [feeling, setFeeling] = useState("");
  const [activity, setActivity] = useState("");
  const [location, setLocation] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // State for expanded/collapsed view
  const [isExpanded, setIsExpanded] = useState(false);

  // States for different sections
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const [showFeelingActivityPicker, setShowFeelingActivityPicker] = useState(false);
  const [showPrivacyDropdown, setShowPrivacyDropdown] = useState(false);

  // Schedule Post states
  const [isScheduled, setIsScheduled] = useState(false);
  const [scheduledDate, setScheduledDate] = useState("");
  const [scheduledTime, setScheduledTime] = useState("");



  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [content, isExpanded]);

  // Close privacy dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showPrivacyDropdown) {
        const target = event.target as Element;
        if (!target.closest('.privacy-dropdown')) {
          setShowPrivacyDropdown(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showPrivacyDropdown]);

  const { getRootProps: getImageRootProps, getInputProps: getImageInputProps } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".gif", ".webp"],
    },
    maxFiles: 5,
    onDrop: async (acceptedFiles) => {
      if (acceptedFiles.length === 0) return;

      // Validate images before adding them
      const validationResults = await validateImageFiles(acceptedFiles);

      if (!areAllValidationResultsValid(validationResults)) {
        const errors = getValidationErrors(validationResults);
        alert(`ছবি validation এ সমস্যা:\n${errors.join('\n')}`);
        return;
      }

      setImages((prev) => [...prev, ...acceptedFiles].slice(0, 5));
    },
  });

  const { getRootProps: getVideoRootProps, getInputProps: getVideoInputProps } = useDropzone({
    accept: {
      "video/*": [".mp4", ".webm", ".ogg"],
    },
    maxFiles: 2,
    onDrop: (acceptedFiles) => {
      setVideos((prev) => [...prev, ...acceptedFiles].slice(0, 2));
    },
  });

  const handleEmojiClick = (emojiData: any) => {
    if (textareaRef.current) {
      const start = textareaRef.current.selectionStart;
      const end = textareaRef.current.selectionEnd;
      const newContent = content.substring(0, start) + emojiData.emoji + content.substring(end);
      setContent(newContent);

      // Focus back on textarea and set cursor position after the inserted emoji
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
          const newCursorPos = start + emojiData.emoji.length;
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
        }
      }, 10);
    }
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!content.trim() && images.length === 0 && videos.length === 0) return;

    // Validate schedule if enabled
    if (isScheduled && !isValidSchedule()) {
      alert("Please select a valid future date and time for scheduling.");
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload images and videos to Cloudinary and get back URLs
      const imageUrls: string[] = [];
      const videoUrls: string[] = [];

      if (images.length > 0 || videos.length > 0) {
        // Upload images to Cloudinary
        if (images.length > 0) {
          try {
            const uploadedImageUrls = await uploadMultipleToCloudinary(images);

            // Validate uploaded URLs
            if (!uploadedImageUrls || uploadedImageUrls.length !== images.length) {
              throw new Error(`Image upload validation failed: Expected ${images.length} images, got ${uploadedImageUrls?.length || 0}`);
            }

            imageUrls.push(...uploadedImageUrls);

          } catch (uploadError) {
            alert(`ছবি আপলোড করতে সমস্যা হয়েছে: ${uploadError instanceof Error ? uploadError.message : 'অজানা সমস্যা'}। অনুগ্রহ করে আবার চেষ্টা করুন।`);
            return;
          }
        }

        // For videos, we're still using the simulated approach for now
        // In a real implementation, you would upload videos to Cloudinary as well
        // Note: Video uploads can be large and may require different handling
        videos.forEach(() => {
          videoUrls.push(`https://example.com/video-${Date.now()}.mp4`);
        });
      }

      const postData = {
        content: content || "",
        images: imageUrls.length > 0 ? imageUrls : undefined,
        videos: videoUrls.length > 0 ? videoUrls : undefined,
        privacy,
        backgroundColor: backgroundColor || undefined,
        feeling: feeling || undefined,
        activity: activity || undefined,
        location: location || undefined,
        isScheduled,
        scheduledDate: isScheduled ? scheduledDate : undefined,
        scheduledTime: isScheduled ? scheduledTime : undefined,
        scheduledDateTime: isScheduled && scheduledDate && scheduledTime
          ? new Date(`${scheduledDate}T${scheduledTime}`).toISOString()
          : undefined,

      };

      const response = await fetch("/api/posts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(postData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to create post: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();

      // Reset form
      setContent("");
      setImages([]);
      setVideos([]);
      setPrivacy("public");
      setActiveSection(null);
      setBackgroundColor("");
      setIsGradient(false);
      setFeeling("");
      setActivity("");
      setLocation("");
      setIsExpanded(false);
      setShowMoreOptions(false);
      setIsScheduled(false);
      setScheduledDate("");
      setScheduledTime("");


      // Emit event to notify NewsFeed component about the new post
      eventBus.emit('post-created');

      // Refresh the feed
      router.refresh();

    } catch (error) {
      // Show user-friendly error message
      alert(`পোস্ট তৈরি করতে সমস্যা হয়েছে: ${error instanceof Error ? error.message : 'অজানা সমস্যা'}। অনুগ্রহ করে আবার চেষ্টা করুন।`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const removeImage = (index: number) => {
    setImages((prev) => prev.filter((_, i) => i !== index));
  };

  const removeVideo = (index: number) => {
    setVideos((prev) => prev.filter((_, i) => i !== index));
  };

  const resetFeelingActivity = () => {
    setFeeling("");
    setActivity("");
  };

  // Handle expanding the post form
  const handleExpandForm = () => {
    setIsExpanded(true);
    // Focus the textarea after expanding
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }, 10);
  };

  // Handle closing the expanded form
  const handleCloseForm = () => {
    setIsExpanded(false);
    // Reset all states when closing
    setContent("");
    setImages([]);
    setVideos([]);
    setPrivacy("public");
    setBackgroundColor("");
    setIsGradient(false);
    setFeeling("");
    setActivity("");
    setLocation("");
    setActiveSection(null);
    setShowMoreOptions(false);
    setShowPrivacyDropdown(false);
    setIsScheduled(false);
    setScheduledDate("");
    setScheduledTime("");
  };

  // Toggle a section
  const toggleSection = (section: string) => {
    if (activeSection === section) {
      setActiveSection(null);
    } else {
      setActiveSection(section);
    }
  };

  // Set background color
  const handleSetBackground = (color: string, isGradient: boolean) => {
    setBackgroundColor(color);
    setIsGradient(isGradient);
  };

  // Get current privacy option
  const getCurrentPrivacyOption = () => {
    return privacyOptions.find(option => option.value === privacy) || privacyOptions[0];
  };

  // Get current background color option
  const getCurrentBackgroundColor = () => {
    return backgroundColors.find(color => color.value === backgroundColor) || backgroundColors[0];
  };

  // Handle privacy change
  const handlePrivacyChange = (newPrivacy: "public" | "subscribers" | "private") => {
    setPrivacy(newPrivacy);
    setShowPrivacyDropdown(false);
  };

  // Handle schedule toggle
  const handleScheduleToggle = () => {
    setIsScheduled(!isScheduled);
    if (!isScheduled) {
      // Set default date and time when enabling schedule
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const dateStr = tomorrow.toISOString().split('T')[0];
      const timeStr = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

      setScheduledDate(dateStr);
      setScheduledTime(timeStr);
    } else {
      // Clear schedule when disabling
      setScheduledDate("");
      setScheduledTime("");
    }
  };

  // Get minimum date (today)
  const getMinDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  // Get minimum time for today
  const getMinTime = () => {
    const now = new Date();
    const selectedDate = new Date(scheduledDate);
    const today = new Date();

    // If selected date is today, minimum time is current time + 5 minutes
    if (selectedDate.toDateString() === today.toDateString()) {
      const minTime = new Date(now.getTime() + 5 * 60000); // Add 5 minutes
      return `${String(minTime.getHours()).padStart(2, '0')}:${String(minTime.getMinutes()).padStart(2, '0')}`;
    }

    return "00:00";
  };

  // Validate scheduled date and time
  const isValidSchedule = () => {
    if (!isScheduled) return true;
    if (!scheduledDate || !scheduledTime) return false;

    const scheduledDateTime = new Date(`${scheduledDate}T${scheduledTime}`);
    const now = new Date();

    return scheduledDateTime > now;
  };

  if (!session?.user) {
    return null;
  }

  // Collapsed view (Facebook-like)
  if (!isExpanded) {
    return (
      <div
        className="rounded-xl bg-white p-4 shadow-sm transition-all duration-200 hover:shadow-md cursor-pointer"
        onClick={handleExpandForm}
      >
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 flex-shrink-0 rounded-full overflow-hidden shadow-sm border border-gray-100">
            {session.user.image ? (
              <Image
                src={session.user.image}
                alt={session.user.name || "Profile"}
                width={40}
                height={40}
                className="rounded-full object-cover"
              />
            ) : (
              <div className="h-full w-full flex items-center justify-center bg-blue-100 text-blue-500">
                {session.user.name?.charAt(0).toUpperCase() || "U"}
              </div>
            )}
          </div>
          <div className="flex-1 rounded-full bg-gray-50 px-4 py-2.5 text-gray-500 hover:bg-gray-100 transition-colors duration-200">
            What's on your mind, {session.user.name?.split(' ')[0]}?
          </div>
        </div>

        <div className="mt-4 pt-3 border-t border-gray-100">
          <div className="flex justify-center gap-2">
            <button
              type="button"
              className="flex-1 flex items-center justify-center py-2.5 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200"
              onClick={(e) => {
                e.stopPropagation();
                handleExpandForm();
                setTimeout(() => setActiveSection('media'), 100);
              }}
            >
              <PhotoIcon className="w-5 h-5 mr-2 text-red-500" />
              Photo/Video
            </button>
            <button
              type="button"
              className="flex-1 flex items-center justify-center py-2.5 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200"
              onClick={(e) => {
                e.stopPropagation();
                handleExpandForm();
                setTimeout(() => setShowFeelingActivityPicker(true), 100);
              }}
            >
              <FaceSmileIcon className="w-5 h-5 mr-2 text-yellow-500" />
              Feeling
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Expanded view (Facebook-like)
  return (
    <div className="rounded-xl bg-white p-5 shadow-md transition-all duration-300">
      <form onSubmit={handleSubmit}>
        {/* Header with close button */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 flex-shrink-0 rounded-full overflow-hidden shadow-sm border border-gray-100">
              {session.user.image ? (
                <Image
                  src={session.user.image}
                  alt={session.user.name || "Profile"}
                  width={40}
                  height={40}
                  className="rounded-full object-cover"
                />
              ) : (
                <div className="h-full w-full flex items-center justify-center bg-blue-100 text-blue-500">
                  {session.user.name?.charAt(0).toUpperCase() || "U"}
                </div>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900">Create Post</h3>
            </div>
          </div>
          <button
            type="button"
            onClick={handleCloseForm}
            className="rounded-full p-1.5 text-gray-400 hover:text-gray-500 hover:bg-gray-100 transition-colors duration-200"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="flex items-start space-x-3 mb-4">
          <div className="min-w-0 flex-1">
            {/* Post content area with enhanced background */}
            <div
              className={`rounded-xl px-6 py-4 focus-within:ring-2 focus-within:ring-blue-400 transition-all duration-300 overflow-hidden shadow-sm ${
                backgroundColor ? 'border-0' : 'border border-gray-200'
              }`}
              style={backgroundColor ?
                (isGradient ? { background: backgroundColor } : { backgroundColor })
                : { backgroundColor: '#f8f9fa' }
              }
            >
              {/* Content preview when background is selected */}
              {backgroundColor && (
                <div className="mb-3 text-xs font-medium opacity-75"
                  style={{
                    color: getCurrentBackgroundColor()?.textColor || '#374151'
                  }}
                >
                  ✨ Colored Post
                </div>
              )}

              <textarea
                ref={textareaRef}
                rows={backgroundColor ? 4 : 3}
                name="content"
                id="content"
                className={`block w-full resize-none border-0 py-2 placeholder:opacity-60 focus:ring-0 text-lg leading-relaxed bg-transparent transition-all duration-300 font-medium`}
                placeholder={backgroundColor ?
                  `Share your thoughts with a splash of color...` :
                  `What is on your mind? #Hashtag.. @Mention.. Link..`
                }
                value={content}
                onChange={(e) => setContent(e.target.value)}
                style={{
                  color: getCurrentBackgroundColor()?.textColor || '#374151',
                  textShadow: isGradient ? '0 1px 3px rgba(0,0,0,0.3)' : 'none'
                }}
              />

              {/* Feeling/Activity display */}
              {(feeling || activity) && (
                <div className="mt-3 flex items-center text-sm opacity-90"
                  style={{ color: getCurrentBackgroundColor()?.textColor || '#6b7280' }}>
                  {feeling && (
                    <span className="mr-3 flex items-center bg-white bg-opacity-20 px-3 py-1 rounded-full">
                      <span className="mr-1 text-base">{feelings.find(f => f.name === feeling)?.emoji}</span>
                      Feeling {feeling}
                    </span>
                  )}
                  {activity && (
                    <span className="mr-3 flex items-center bg-white bg-opacity-20 px-3 py-1 rounded-full">
                      <span className="mr-1 text-base">{activities.find(a => a.name === activity)?.emoji}</span>
                      {activity}
                    </span>
                  )}
                  <button
                    type="button"
                    className="ml-1 opacity-60 hover:opacity-100 transition-opacity duration-200 bg-white bg-opacity-20 rounded-full h-6 w-6 flex items-center justify-center"
                    onClick={resetFeelingActivity}
                    style={{ color: getCurrentBackgroundColor()?.textColor || '#6b7280' }}
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </div>
              )}

              {/* Location display */}
              {location && (
                <div className="mt-3 flex items-center text-sm opacity-90"
                  style={{ color: getCurrentBackgroundColor()?.textColor || '#6b7280' }}>
                  <span className="mr-3 flex items-center bg-white bg-opacity-20 px-3 py-1 rounded-full">
                    <MapPinIcon className="h-4 w-4 mr-1" /> {location}
                  </span>
                  <button
                    type="button"
                    className="ml-1 opacity-60 hover:opacity-100 transition-opacity duration-200 bg-white bg-opacity-20 rounded-full h-6 w-6 flex items-center justify-center"
                    onClick={() => setLocation("")}
                    style={{ color: getCurrentBackgroundColor()?.textColor || '#6b7280' }}
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </div>
              )}


            </div>

            {/* Media upload area */}
            <Transition
              show={activeSection === 'media'}
              as={Fragment}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 translate-y-1"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-1"
            >
              <div className="mt-3 rounded-xl border border-gray-200 p-4 bg-white shadow-sm">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-medium text-gray-700">Add Photos or Videos</h3>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-500"
                    onClick={() => setActiveSection(null)}
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <div className="flex space-x-4 mb-2">
                  <div className="flex-1">
                    <div {...getImageRootProps({ className: "dropzone" })} className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
                      <input {...getImageInputProps()} />
                      <PhotoIcon className="h-8 w-8 mx-auto text-gray-400" />
                      <p className="mt-1 text-sm text-gray-500">
                        Drag & drop images here, or click to select
                      </p>
                    </div>
                  </div>
                  <div className="flex-1">
                    <div {...getVideoRootProps({ className: "dropzone" })} className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
                      <input {...getVideoInputProps()} />
                      <VideoCameraIcon className="h-8 w-8 mx-auto text-gray-400" />
                      <p className="mt-1 text-sm text-gray-500">
                        Drag & drop videos here, or click to select
                      </p>
                    </div>
                  </div>
                </div>

                {/* Image previews */}
                {images.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Images</h4>
                    <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4">
                      {images.map((file, index) => (
                        <div key={index} className="relative group">
                          <div className="overflow-hidden rounded-lg bg-gray-100">
                            <Image
                              src={URL.createObjectURL(file)}
                              alt={`Upload ${index + 1}`}
                              width={300}
                              height={300}
                              className="w-full h-auto object-contain transition-transform duration-200 group-hover:scale-105"
                            />
                          </div>
                          <button
                            type="button"
                            className="absolute -right-2 -top-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-white shadow-sm opacity-90 hover:opacity-100 transition-opacity duration-200"
                            onClick={() => removeImage(index)}
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Video previews */}
                {videos.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Videos</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {videos.map((file, index) => (
                        <div key={index} className="relative group">
                          <div className="aspect-video overflow-hidden rounded-lg bg-gray-100">
                            <video
                              src={URL.createObjectURL(file)}
                              controls
                              className="h-full w-full object-cover"
                            />
                          </div>
                          <button
                            type="button"
                            className="absolute -right-2 -top-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-white shadow-sm opacity-90 hover:opacity-100 transition-opacity duration-200"
                            onClick={() => removeVideo(index)}
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Transition>

            {/* Location input */}
            <Transition
              show={activeSection === 'location'}
              as={Fragment}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 translate-y-1"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-1"
            >
              <div className="mt-3 rounded-xl border border-gray-200 p-4 bg-white shadow-sm">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-medium text-gray-700">Add Location</h3>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-500"
                    onClick={() => setActiveSection(null)}
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <div className="relative">
                  <MapPinIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Where are you?"
                    className="w-full rounded-lg border border-gray-300 pl-10 pr-4 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                  />
                </div>
              </div>
            </Transition>

            {/* Emoji picker */}
            <Transition
              show={activeSection === 'emoji'}
              as={Fragment}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 translate-y-1"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-1"
            >
              <div className="mt-3 relative z-10">
                <div className="rounded-xl border border-gray-200 p-2 bg-white shadow-sm">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-medium text-gray-700">Add Emoji</h3>
                    <button
                      type="button"
                      className="text-gray-400 hover:text-gray-500"
                      onClick={() => setActiveSection(null)}
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  </div>
                  <EmojiPicker
                    onEmojiClick={handleEmojiClick}
                    width="100%"
                    height="350px"
                  />
                </div>
              </div>
            </Transition>

            {/* Background color picker */}
            <Transition
              show={activeSection === 'background'}
              as={Fragment}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 translate-y-1"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-1"
            >
              <div className="mt-3 rounded-xl border border-gray-200 p-5 bg-white shadow-lg">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h3 className="text-base font-semibold text-gray-800">Choose Background</h3>
                    <p className="text-xs text-gray-500 mt-1">Make your post stand out with colors</p>
                  </div>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-1 transition-colors duration-200"
                    onClick={() => setActiveSection(null)}
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                {/* Color Grid */}
                <div className="grid grid-cols-6 gap-3">
                  {backgroundColors.map((color) => (
                    <div key={color.name} className="flex flex-col items-center">
                      <button
                        type="button"
                        className={`h-12 w-12 rounded-xl border-2 transition-all duration-300 hover:scale-110 hover:shadow-lg ${
                          backgroundColor === color.value
                            ? 'ring-3 ring-blue-500 ring-offset-2 scale-110 shadow-lg'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        style={color.gradient ? { background: color.value } : { backgroundColor: color.value || '#f9fafb' }}
                        onClick={() => handleSetBackground(color.value, color.gradient)}
                        title={color.name}
                      >
                        {color.value === "" && (
                          <span className="text-gray-400 text-xs font-medium">None</span>
                        )}
                      </button>
                      <span className="text-xs text-gray-600 mt-1 text-center leading-tight max-w-[60px] truncate">
                        {color.name}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Current Selection Preview */}
                {backgroundColor && (
                  <div className="mt-4 p-3 rounded-lg border border-gray-200 bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Selected: {getCurrentBackgroundColor()?.name}</p>
                        <p className="text-xs text-gray-500">Your post will have this background</p>
                      </div>
                      <div
                        className="h-8 w-16 rounded border border-gray-200 shadow-sm"
                        style={isGradient ? { background: backgroundColor } : { backgroundColor }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </Transition>

            {/* Feeling/Activity picker */}
            <Dialog
              open={showFeelingActivityPicker}
              onClose={() => setShowFeelingActivityPicker(false)}
              className="relative z-50"
            >
              <Transition
                show={showFeelingActivityPicker}
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
              </Transition>

              <div className="fixed inset-0 flex items-center justify-center p-4">
                <Transition
                  show={showFeelingActivityPicker}
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 scale-95"
                  enterTo="opacity-100 scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 scale-100"
                  leaveTo="opacity-0 scale-95"
                >
                  <DialogPanel className="w-full max-w-md rounded-xl bg-white p-6 shadow-xl">
                    <DialogTitle className="text-lg font-medium text-gray-900 flex items-center">
                      <FaceSmileIcon className="h-5 w-5 mr-2 text-blue-500" />
                      How are you feeling?
                    </DialogTitle>
                    <button
                      type="button"
                      className="absolute top-4 right-4 text-gray-400 hover:text-gray-500 transition-colors duration-200"
                      onClick={() => setShowFeelingActivityPicker(false)}
                    >
                      <XMarkIcon className="h-6 w-6" />
                    </button>

                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Feelings</h3>
                      <div className="mt-2 grid grid-cols-3 gap-2">
                        {feelings.map((item) => (
                          <button
                            key={item.name}
                            type="button"
                            className={`flex items-center space-x-2 rounded-lg p-2 transition-colors duration-200 ${feeling === item.name ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                            onClick={() => {
                              setFeeling(item.name);
                              setActivity("");
                              setShowFeelingActivityPicker(false);
                            }}
                          >
                            <span className="text-xl">{item.emoji}</span>
                            <span className="text-sm">{item.name}</span>
                          </button>
                        ))}
                      </div>
                    </div>

                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Activities</h3>
                      <div className="mt-2 grid grid-cols-3 gap-2">
                        {activities.map((item) => (
                          <button
                            key={item.name}
                            type="button"
                            className={`flex items-center space-x-2 rounded-lg p-2 transition-colors duration-200 ${activity === item.name ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
                            onClick={() => {
                              setActivity(item.name);
                              setFeeling("");
                              setShowFeelingActivityPicker(false);
                            }}
                          >
                            <span className="text-xl">{item.emoji}</span>
                            <span className="text-sm">{item.name}</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  </DialogPanel>
                </Transition>
              </div>
            </Dialog>

            {/* Schedule Post toggle */}
            <div className="mt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="w-6 h-6 mr-2 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" />
                  </svg>
                  <span className="text-sm font-medium">Schedule Post</span>
                </div>
                <button
                  type="button"
                  onClick={handleScheduleToggle}
                  className={`relative inline-flex h-6 w-12 items-center rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    isScheduled ? 'bg-blue-500' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform duration-300 ${
                      isScheduled ? 'translate-x-7' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Schedule Date and Time Inputs */}
              {isScheduled && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center mb-3">
                    <svg className="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm font-medium text-blue-800">Schedule your post</span>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label htmlFor="schedule-date" className="block text-xs font-medium text-gray-700 mb-1">
                        Date
                      </label>
                      <input
                        type="date"
                        id="schedule-date"
                        value={scheduledDate}
                        min={getMinDate()}
                        onChange={(e) => setScheduledDate(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label htmlFor="schedule-time" className="block text-xs font-medium text-gray-700 mb-1">
                        Time
                      </label>
                      <input
                        type="time"
                        id="schedule-time"
                        value={scheduledTime}
                        min={getMinTime()}
                        onChange={(e) => setScheduledTime(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>

                  {scheduledDate && scheduledTime && (
                    <div className="mt-3 p-2 bg-white rounded border border-blue-200">
                      <div className="flex items-center text-sm">
                        <svg className="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">
                          Post will be published on{" "}
                          <span className="font-medium text-blue-600">
                            {new Date(`${scheduledDate}T${scheduledTime}`).toLocaleString()}
                          </span>
                        </span>
                      </div>
                    </div>
                  )}

                  {!isValidSchedule() && scheduledDate && scheduledTime && (
                    <div className="mt-3 p-2 bg-red-50 rounded border border-red-200">
                      <div className="flex items-center text-sm text-red-700">
                        <svg className="w-4 h-4 mr-2 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <span>Scheduled time must be in the future</span>
                      </div>
                    </div>
                  )}
                </div>
              )}


            </div>

            {/* Privacy selector and Post button */}
            <div className="mt-6 flex items-center justify-between">
              <div className="relative privacy-dropdown">
                <button
                  type="button"
                  className="flex items-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                  onClick={() => setShowPrivacyDropdown(!showPrivacyDropdown)}
                >
                  <span className="mr-2 text-lg">{getCurrentPrivacyOption().icon}</span>
                  {getCurrentPrivacyOption().label}
                  <svg className={`w-4 h-4 ml-2 transition-transform duration-200 ${showPrivacyDropdown ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>

                {/* Privacy Dropdown */}
                {showPrivacyDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                    <div className="py-2">
                      {privacyOptions.map((option) => (
                        <button
                          key={option.value}
                          type="button"
                          className={`w-full flex items-start px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 ${
                            privacy === option.value ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                          }`}
                          onClick={() => handlePrivacyChange(option.value as "public" | "subscribers" | "private")}
                        >
                          <span className="text-xl mr-3 mt-0.5">{option.icon}</span>
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">{option.label}</div>
                            <div className="text-sm text-gray-500 mt-0.5">{option.description}</div>
                          </div>
                          {privacy === option.value && (
                            <svg className="w-5 h-5 text-blue-500 mt-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <Button
                type="submit"
                disabled={isSubmitting || (!content.trim() && images.length === 0 && videos.length === 0) || (isScheduled && !isValidSchedule())}
                isLoading={isSubmitting}
                className="rounded-lg px-8 py-2 bg-blue-600 hover:bg-blue-700 transition-colors duration-200 font-medium"
              >
                {isScheduled ? "Schedule Post" : "Post"}
              </Button>
            </div>

            {/* Action buttons - Facebook-like style */}
            <div className="mt-4">
              {/* Grid layout for options */}
              <div className="flex justify-start gap-2 mb-2">
                <button
                  type="button"
                  className={`flex-1 flex items-center justify-start px-4 py-3 text-sm font-medium rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors duration-200`}
                  onClick={() => toggleSection('media')}
                >
                  <svg className="w-6 h-6 mr-3 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 3H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V5a2 2 0 00-2-2zm0 16H5V5h14v14zm-5.04-6.71l-2.75 3.54-1.96-2.36L6.5 17h11l-3.54-4.71z" />
                  </svg>
                  Upload Photos
                </button>
                <button
                  type="button"
                  className="flex-1 flex items-center justify-start px-4 py-3 text-sm font-medium bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
                  onClick={() => setShowFeelingActivityPicker(true)}
                >
                  <svg className="w-6 h-6 mr-3 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-6c.78 0 1.44-.45 1.76-1.1l1.58.52c-.56 1.17-1.71 2-3.08 2-1.94 0-3.5-1.56-3.5-3.5s1.56-3.5 3.5-3.5c1.43 0 2.61.95 3.1 2.21l-1.58.52c-.32-.65-.98-1.1-1.76-1.1-.89 0-1.61.72-1.61 1.61 0 .89.72 1.61 1.61 1.61zm7.5 0c.78 0 1.44-.45 1.76-1.1l1.58.52c-.56 1.17-1.71 2-3.08 2-1.94 0-3.5-1.56-3.5-3.5s1.56-3.5 3.5-3.5c1.43 0 2.61.95 3.1 2.21l-1.58.52c-.32-.65-.98-1.1-1.76-1.1-.89 0-1.61.72-1.61 1.61 0 .89.72 1.61 1.61 1.61z" />
                  </svg>
                  Feelings/Activity
                </button>
              </div>

              <div className="flex justify-start gap-2 mb-2">
                <button
                  type="button"
                  className="flex-1 flex items-center justify-start px-4 py-3 text-sm font-medium bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
                  onClick={() => {
                    setShowMoreOptions(true);
                    toggleSection('background');
                  }}
                >
                  <svg className="w-6 h-6 mr-3 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 22C6.49 22 2 17.51 2 12S6.49 2 12 2s10 4.04 10 9c0 3.31-2.69 6-6 6h-1.77c-.28 0-.5.22-.5.5 0 .12.05.23.13.33.41.47.64 1.06.64 1.67A2.5 2.5 0 0112 22zm0-18c-4.41 0-8 3.59-8 8s3.59 8 8 8c.28 0 .5-.22.5-.5a.54.54 0 00-.14-.35c-.41-.46-.63-1.05-.63-1.65a2.5 2.5 0 012.5-2.5H16c2.21 0 4-1.79 4-4 0-3.86-3.59-7-8-7z" />
                  </svg>
                  Colored Posts
                </button>
                <button
                  type="button"
                  className="flex-1 flex items-center justify-start px-4 py-3 text-sm font-medium bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
                  onClick={() => {
                    setShowMoreOptions(true);
                  }}
                >
                  <svg className="w-6 h-6 mr-3 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4zM14 13h-3v3H9v-3H6v-2h3V8h2v3h3v2z" />
                  </svg>
                  Upload Video
                </button>
              </div>

              <div className="flex justify-start gap-2">
                <button
                  type="button"
                  className="flex-1 flex items-center justify-start px-4 py-3 text-sm font-medium bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
                  onClick={() => {
                    setShowMoreOptions(true);
                  }}
                >
                  <svg className="w-6 h-6 mr-3 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z" />
                  </svg>
                  Upload Reel
                </button>
                <Link
                  href="/blogs/create"
                  className="flex-1 flex items-center justify-start px-4 py-3 text-sm font-medium bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
                >
                  <svg className="w-6 h-6 mr-3 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14h-2V9h-2V7h4v10z" />
                  </svg>
                  Create Blog
                </Link>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}