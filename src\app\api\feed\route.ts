import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts, users, fanPagePosts, fanPages, fanPageFollowers, fanPagePostLikes, subscriptions } from "@/lib/db/schema";
import { desc, eq, and, inArray, or, sql } from "drizzle-orm";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const currentUserId = session.user.id;

    // Get pagination parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 20); // Max 20 posts per page
    const offset = (page - 1) * limit;

    // Get user's subscriptions to filter content
    const userSubscriptions = await db.query.subscriptions.findMany({
      where: eq(subscriptions.subscriberId, currentUserId),
      columns: {
        targetUserId: true,
      },
    });

    const subscribedUserIds = userSubscriptions.map(sub => sub.targetUserId);

    // Get followed fan pages
    const followedPages = await db
      .select({ fanPageId: fanPageFollowers.fanPageId })
      .from(fanPageFollowers)
      .where(eq(fanPageFollowers.userId, currentUserId));

    const followedPageIds = followedPages.map(fp => fp.fanPageId);

    // Fetch user posts with privacy filtering and pagination
    const userPosts = await db.query.posts.findMany({
      where: or(
        // Public posts from anyone
        eq(posts.privacy, 'public'),
        // User's own posts (all privacy levels)
        eq(posts.userId, currentUserId),
        // Subscribers-only posts from users the current user is subscribed to
        ...(subscribedUserIds.length > 0 ? [
          and(
            eq(posts.privacy, 'subscribers'),
            sql`${posts.userId} IN (${subscribedUserIds.map(id => `'${id}'`).join(',')})`
          )
        ] : [])
      ),
      orderBy: [desc(posts.createdAt)],
      limit: Math.ceil(limit / 2), // Split between user posts and fan page posts
      offset: Math.floor(offset / 2),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        likes: true,
        comments: {
          columns: {
            id: true,
          },
        },
        sharedPost: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                username: true,
                image: true,
              },
            },
          },
        },
        shares: true,
      },
    });

    // Fetch fan page posts from followed pages
    let fanPagePostsData: any[] = [];
    if (followedPageIds.length > 0) {
      fanPagePostsData = await db
        .select({
          id: fanPagePosts.id,
          content: fanPagePosts.content,
          images: fanPagePosts.images,
          videos: fanPagePosts.videos,
          type: fanPagePosts.type,
          likeCount: fanPagePosts.likeCount,
          commentCount: fanPagePosts.commentCount,
          shareCount: fanPagePosts.shareCount,
          viewCount: fanPagePosts.viewCount,
          createdAt: fanPagePosts.createdAt,
          fanPage: {
            id: fanPages.id,
            name: fanPages.name,
            username: fanPages.username,
            profileImage: fanPages.profileImage,
            isVerified: fanPages.isVerified,
          },
        })
        .from(fanPagePosts)
        .leftJoin(fanPages, eq(fanPagePosts.fanPageId, fanPages.id))
        .where(
          and(
            inArray(fanPagePosts.fanPageId, followedPageIds),
            eq(fanPagePosts.isPublished, true)
          )
        )
        .orderBy(desc(fanPagePosts.createdAt))
        .limit(Math.floor(limit / 2))
        .offset(Math.ceil(offset / 2));
    }

    // Transform user posts to unified format
    const transformedUserPosts = userPosts.map(post => ({
      id: post.id,
      content: post.content,
      images: post.images,
      videos: post.videos,
      privacy: post.privacy,
      backgroundColor: post.backgroundColor,
      feeling: post.feeling,
      activity: post.activity,
      location: post.location,
      formattedContent: post.formattedContent,
      createdAt: post.createdAt,
      type: 'user_post' as const,
      user: post.user,
      fanPage: null,
      _count: {
        likes: post.likes?.length || 0,
        dislikes: 0, // User posts don't have dislikes in the current schema
        comments: post.comments?.length || 0,
        shares: post.shares?.length || 0,
      },
      liked: post.likes?.some(like => like.userId === currentUserId) || false,
      disliked: false,
      sharedPost: post.sharedPost,
    }));

    // Get fan page post likes/dislikes for current user
    let fanPagePostLikesData: any[] = [];
    if (fanPagePostsData.length > 0) {
      const fanPagePostIds = fanPagePostsData.map(post => post.id);
      fanPagePostLikesData = await db
        .select({
          postId: fanPagePostLikes.fanPagePostId,
          type: fanPagePostLikes.type,
        })
        .from(fanPagePostLikes)
        .where(
          and(
            inArray(fanPagePostLikes.fanPagePostId, fanPagePostIds),
            eq(fanPagePostLikes.userId, currentUserId)
          )
        );
    }

    // Transform fan page posts to unified format
    const transformedFanPagePosts = fanPagePostsData.map(post => {
      const userLike = fanPagePostLikesData.find(like => like.postId === post.id && like.type === 'like');
      const userDislike = fanPagePostLikesData.find(like => like.postId === post.id && like.type === 'angry');

      return {
        id: post.id,
        content: post.content,
        images: post.images,
        videos: post.videos,
        privacy: 'public', // Fan page posts are always public
        backgroundColor: null,
        feeling: null,
        activity: null,
        location: null,
        formattedContent: null,
        createdAt: post.createdAt,
        type: 'fan_page_post' as const,
        user: null,
        fanPage: post.fanPage,
        _count: {
          likes: post.likeCount || 0,
          dislikes: 0, // We don't track dislike count separately for fan pages
          comments: post.commentCount || 0,
          shares: post.shareCount || 0,
        },
        liked: !!userLike,
        disliked: !!userDislike,
        sharedPost: null,
      };
    });

    // Combine and sort all posts by creation date
    const allPosts = [...transformedUserPosts, ...transformedFanPagePosts]
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Calculate pagination metadata
    // hasMore should be true if we got the full limit of posts, indicating there might be more
    const hasMore = allPosts.length >= limit;

    // For total calculation, we need to count all available posts with privacy filtering
    const totalUserPostsCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(posts)
      .where(or(
        eq(posts.privacy, 'public'),
        eq(posts.userId, currentUserId),
        ...(subscribedUserIds.length > 0 ? [
          and(
            eq(posts.privacy, 'subscribers'),
            sql`${posts.userId} IN (${subscribedUserIds.map(id => `'${id}'`).join(',')})`
          )
        ] : [])
      ));

    const totalFanPagePostsCount = followedPageIds.length > 0 ? await db
      .select({ count: sql<number>`count(*)` })
      .from(fanPagePosts)
      .where(
        and(
          inArray(fanPagePosts.fanPageId, followedPageIds),
          eq(fanPagePosts.isPublished, true)
        )
      ) : [{ count: 0 }];

    const totalCount = (totalUserPostsCount[0]?.count || 0) + (totalFanPagePostsCount[0]?.count || 0);
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      data: allPosts,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore,
        totalPages,
        currentCount: allPosts.length,
      },
    });

  } catch (error) {
    console.error("Error fetching feed:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
