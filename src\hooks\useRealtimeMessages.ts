/**
 * Real-time Messages Hook
 * Custom hook for managing real-time messaging functionality
 */

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { realtimeMessageService, MessageEvent, TypingIndicator } from '@/lib/services/realtimeMessageService';

export interface UseRealtimeMessagesProps {
  conversationId?: string;
  onNewMessage?: (message: any) => void;
  onMessageRead?: (messageId: string) => void;
  onTypingChange?: (typingUsers: TypingIndicator[]) => void;
}

export function useRealtimeMessages({
  conversationId,
  onNewMessage,
  onMessageRead,
  onTypingChange,
}: UseRealtimeMessagesProps) {
  const { data: session } = useSession();
  const [isConnected, setIsConnected] = useState(false);
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);

  // Initialize real-time service
  useEffect(() => {
    if (session?.user?.id) {
      realtimeMessageService.initialize(session.user.id);
      setIsConnected(true);

      return () => {
        realtimeMessageService.cleanup();
        setIsConnected(false);
      };
    }
  }, [session?.user?.id]);

  // Subscribe to conversation
  useEffect(() => {
    if (conversationId && session?.user?.id && isConnected) {
      const handleMessageEvent = (event: MessageEvent) => {
        switch (event.type) {
          case 'new_message':
            onNewMessage?.(event.data);
            break;
          case 'message_read':
            onMessageRead?.(event.data.mysql_message_id);
            break;
        }
      };

      const handleTypingChange = (typing: TypingIndicator[]) => {
        setTypingUsers(typing);
        onTypingChange?.(typing);
      };

      realtimeMessageService.subscribeToConversation(conversationId, handleMessageEvent);
      realtimeMessageService.subscribeToTyping(conversationId, handleTypingChange);

      return () => {
        realtimeMessageService.unsubscribeFromConversation(conversationId);
      };
    }
  }, [conversationId, session?.user?.id, isConnected, onNewMessage, onMessageRead, onTypingChange]);

  // Send message
  const sendMessage = useCallback(async (
    messageId: string,
    receiverId: string,
    content: string
  ) => {
    if (!session?.user?.id) return { success: false, error: 'Not authenticated' };

    return await realtimeMessageService.sendRealtimeMessage(
      messageId,
      session.user.id,
      receiverId,
      content
    );
  }, [session?.user?.id]);

  // Mark message as read
  const markAsRead = useCallback(async (messageId: string) => {
    await realtimeMessageService.markMessageAsRead(messageId);
  }, []);

  // Typing functions
  const startTyping = useCallback(() => {
    if (conversationId) {
      realtimeMessageService.startTyping(conversationId);
    }
  }, [conversationId]);

  const stopTyping = useCallback(() => {
    if (conversationId) {
      realtimeMessageService.stopTyping(conversationId);
    }
  }, [conversationId]);

  // Update presence
  const updatePresence = useCallback(async (status: 'online' | 'offline' | 'away') => {
    await realtimeMessageService.updateUserPresence(status);
  }, []);

  return {
    isConnected,
    typingUsers,
    sendMessage,
    markAsRead,
    startTyping,
    stopTyping,
    updatePresence,
  };
}
