/**
 * Real-time Optimization Utilities
 * Performance optimizations and fallback mechanisms for real-time features
 */

import { useCallback, useRef, useEffect } from 'react';

/**
 * Debounce hook for real-time events
 * Prevents excessive API calls during rapid user interactions
 */
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ) as T;

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
}

/**
 * Throttle hook for real-time events
 * Limits the frequency of function calls
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCallRef = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const throttledCallback = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      const timeSinceLastCall = now - lastCallRef.current;

      if (timeSinceLastCall >= delay) {
        lastCallRef.current = now;
        callback(...args);
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(() => {
          lastCallRef.current = Date.now();
          callback(...args);
        }, delay - timeSinceLastCall);
      }
    },
    [callback, delay]
  ) as T;

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return throttledCallback;
}

/**
 * Connection quality monitor
 * Monitors real-time connection quality and adjusts behavior accordingly
 */
export class ConnectionQualityMonitor {
  private latencyHistory: number[] = [];
  private maxHistorySize = 10;
  private connectionStartTime = 0;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  recordLatency(latency: number): void {
    this.latencyHistory.push(latency);
    if (this.latencyHistory.length > this.maxHistorySize) {
      this.latencyHistory.shift();
    }
  }

  getAverageLatency(): number {
    if (this.latencyHistory.length === 0) return 0;
    return this.latencyHistory.reduce((sum, lat) => sum + lat, 0) / this.latencyHistory.length;
  }

  getConnectionQuality(): 'excellent' | 'good' | 'poor' | 'offline' {
    const avgLatency = this.getAverageLatency();
    
    if (avgLatency === 0) return 'offline';
    if (avgLatency < 100) return 'excellent';
    if (avgLatency < 300) return 'good';
    return 'poor';
  }

  shouldReduceFrequency(): boolean {
    return this.getConnectionQuality() === 'poor';
  }

  recordConnectionStart(): void {
    this.connectionStartTime = Date.now();
  }

  recordReconnectAttempt(): void {
    this.reconnectAttempts++;
  }

  shouldAttemptReconnect(): boolean {
    return this.reconnectAttempts < this.maxReconnectAttempts;
  }

  reset(): void {
    this.latencyHistory = [];
    this.reconnectAttempts = 0;
    this.connectionStartTime = 0;
  }
}

/**
 * Fallback mechanism for when real-time fails
 */
export class RealtimeFallback {
  private fallbackInterval: NodeJS.Timeout | null = null;
  private isUsingFallback = false;

  startPolling(callback: () => void, interval: number = 5000): void {
    if (this.fallbackInterval) {
      clearInterval(this.fallbackInterval);
    }

    this.isUsingFallback = true;
    this.fallbackInterval = setInterval(callback, interval);
    console.log('🔄 Real-time fallback: Started polling mode');
  }

  stopPolling(): void {
    if (this.fallbackInterval) {
      clearInterval(this.fallbackInterval);
      this.fallbackInterval = null;
    }
    this.isUsingFallback = false;
    console.log('✅ Real-time fallback: Stopped polling mode');
  }

  isActive(): boolean {
    return this.isUsingFallback;
  }
}

/**
 * Message queue for offline scenarios
 */
export class OfflineMessageQueue {
  private queue: Array<{
    id: string;
    type: 'message' | 'notification' | 'presence';
    data: any;
    timestamp: number;
    retryCount: number;
  }> = [];
  private maxQueueSize = 100;
  private maxRetries = 3;

  add(type: 'message' | 'notification' | 'presence', data: any): string {
    const id = `offline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    this.queue.push({
      id,
      type,
      data,
      timestamp: Date.now(),
      retryCount: 0,
    });

    // Remove oldest items if queue is too large
    if (this.queue.length > this.maxQueueSize) {
      this.queue.shift();
    }

    return id;
  }

  async processQueue(processor: (item: any) => Promise<boolean>): Promise<void> {
    const itemsToProcess = [...this.queue];
    
    for (const item of itemsToProcess) {
      try {
        const success = await processor(item);
        
        if (success) {
          this.remove(item.id);
        } else {
          item.retryCount++;
          if (item.retryCount >= this.maxRetries) {
            this.remove(item.id);
            console.warn(`Offline queue: Max retries reached for item ${item.id}`);
          }
        }
      } catch (error) {
        console.error(`Offline queue: Error processing item ${item.id}:`, error);
        item.retryCount++;
        if (item.retryCount >= this.maxRetries) {
          this.remove(item.id);
        }
      }
    }
  }

  remove(id: string): void {
    this.queue = this.queue.filter(item => item.id !== id);
  }

  clear(): void {
    this.queue = [];
  }

  size(): number {
    return this.queue.length;
  }

  getItems(): Array<any> {
    return [...this.queue];
  }
}

/**
 * Performance metrics collector
 */
export class RealtimeMetrics {
  private metrics: {
    messagesReceived: number;
    messagesSent: number;
    notificationsReceived: number;
    connectionDrops: number;
    averageLatency: number;
    lastActivity: number;
  } = {
    messagesReceived: 0,
    messagesSent: 0,
    notificationsReceived: 0,
    connectionDrops: 0,
    averageLatency: 0,
    lastActivity: Date.now(),
  };

  recordMessageReceived(): void {
    this.metrics.messagesReceived++;
    this.metrics.lastActivity = Date.now();
  }

  recordMessageSent(): void {
    this.metrics.messagesSent++;
    this.metrics.lastActivity = Date.now();
  }

  recordNotificationReceived(): void {
    this.metrics.notificationsReceived++;
    this.metrics.lastActivity = Date.now();
  }

  recordConnectionDrop(): void {
    this.metrics.connectionDrops++;
  }

  updateLatency(latency: number): void {
    this.metrics.averageLatency = latency;
  }

  getMetrics(): typeof this.metrics {
    return { ...this.metrics };
  }

  reset(): void {
    this.metrics = {
      messagesReceived: 0,
      messagesSent: 0,
      notificationsReceived: 0,
      connectionDrops: 0,
      averageLatency: 0,
      lastActivity: Date.now(),
    };
  }

  getHealthScore(): number {
    const now = Date.now();
    const timeSinceActivity = now - this.metrics.lastActivity;
    const activityScore = Math.max(0, 100 - (timeSinceActivity / 1000 / 60)); // Decrease over time
    
    const latencyScore = Math.max(0, 100 - (this.metrics.averageLatency / 10));
    const connectionScore = Math.max(0, 100 - (this.metrics.connectionDrops * 10));
    
    return Math.round((activityScore + latencyScore + connectionScore) / 3);
  }
}

// Export singleton instances
export const connectionMonitor = new ConnectionQualityMonitor();
export const realtimeFallback = new RealtimeFallback();
export const offlineQueue = new OfflineMessageQueue();
export const realtimeMetrics = new RealtimeMetrics();
