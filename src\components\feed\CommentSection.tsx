"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import Image from "next/image";
import { formatTimeAgo } from "@/lib/utils";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { CommentTime } from "@/components/ui/TimeDisplay";
import eventBus from "@/lib/eventBus";

interface Comment {
  id: string;
  content: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
    image: string | null;
  };
  _count: {
    likes: number;
  };
  liked: boolean;
}

interface CommentSectionProps {
  postId: string;
}

export function CommentSection({ postId }: CommentSectionProps) {
  const { data: session } = useSession();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create a memoized fetchComments function that can be reused
  const fetchComments = useCallback(async () => {
    try {
      setIsLoading(true);

      // Fetch comments from the API
      const response = await fetch(`/api/posts/${postId}/comments`);
      if (!response.ok) throw new Error("Failed to fetch comments");
      const data = await response.json();
      setComments(data);
    } catch (error) {
      console.error("Error fetching comments:", error);
    } finally {
      setIsLoading(false);
    }
  }, [postId]);

  // Initial fetch when component mounts
  useEffect(() => {
    fetchComments();
  }, [fetchComments]);

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newComment.trim() || !session?.user) return;

    setIsSubmitting(true);

    try {
      // Call the API to post a comment
      const response = await fetch(`/api/posts/${postId}/comments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: newComment }),
      });

      if (!response.ok) throw new Error("Failed to post comment");

      // Refresh comments
      fetchComments();

      // Emit event to notify that a comment was added
      eventBus.emit('comment-added', postId);

      setNewComment("");
    } catch (error) {
      console.error("Error posting comment:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId: string) => {
    try {
      // Call the API to like/unlike the comment
      const response = await fetch(`/api/comments/${commentId}/like`, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to like comment");
      }

      // Update the state optimistically
      setComments((prevComments) =>
        prevComments.map((comment) => {
          if (comment.id === commentId) {
            const newLiked = !comment.liked;
            return {
              ...comment,
              liked: newLiked,
              _count: {
                ...comment._count,
                likes: comment._count.likes + (newLiked ? 1 : -1),
              },
            };
          }
          return comment;
        })
      );
    } catch (error) {
      console.error("Error liking comment:", error);
    }
  };

  return (
    <div className="mt-3 pt-2 border-t border-gray-100">
      {isLoading ? (
        <div className="flex justify-center py-2">
          <Spinner size="sm" />
        </div>
      ) : null}

      {/* Comments list */}
      <div className="space-y-3 mb-3">
        {comments.length === 0 && !isLoading ? (
          <p className="text-center text-xs text-gray-500 py-2">
            No comments yet. Be the first to comment!
          </p>
        ) : (
          comments.map((comment) => (
            <div key={comment.id} className="flex space-x-2">
              <div className="h-8 w-8 flex-shrink-0 rounded-full bg-gray-200 overflow-hidden">
                {comment.user.image ? (
                  <Image
                    src={comment.user.image}
                    alt={comment.user.name}
                    width={32}
                    height={32}
                    className="rounded-full object-cover"
                  />
                ) : (
                  <div className="h-full w-full flex items-center justify-center bg-gray-300 text-gray-600 text-sm font-bold">
                    {comment.user.name?.charAt(0).toUpperCase() || "U"}
                  </div>
                )}
              </div>
              <div className="flex-1">
                <div className="rounded-2xl bg-gray-100 px-3 py-2">
                  <p className="text-xs font-medium text-gray-900">
                    {comment.user.name}
                  </p>
                  <p className="text-sm text-gray-700">
                    {comment.content}
                  </p>
                </div>
                <div className="mt-1 flex items-center space-x-3 pl-2 text-xs">
                  <button
                    className={`${
                      comment.liked
                        ? "text-blue-600 font-medium"
                        : "text-gray-500 hover:text-blue-600"
                    }`}
                    onClick={() => handleLikeComment(comment.id)}
                  >
                    Like{comment._count.likes > 0 && ` ${comment._count.likes}`}
                  </button>
                  <button className="text-gray-500 hover:text-blue-600">
                    Reply
                  </button>
                  <CommentTime
                    date={comment.createdAt}
                    className="text-gray-400 text-xs"
                    autoUpdate={true}
                  />
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Comment form */}
      <form onSubmit={handleSubmitComment} className="flex items-center space-x-2">
        <div className="h-8 w-8 flex-shrink-0 rounded-full bg-gray-200 overflow-hidden">
          {session?.user?.image ? (
            <Image
              src={session.user.image}
              alt={session.user.name || "User"}
              width={32}
              height={32}
              className="rounded-full object-cover"
            />
          ) : (
            <div className="h-full w-full flex items-center justify-center bg-gray-300 text-gray-600 text-sm font-bold">
              {session?.user?.name?.charAt(0).toUpperCase() || "U"}
            </div>
          )}
        </div>
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="Write a comment..."
            className="w-full rounded-full border border-gray-300 bg-gray-100 px-3 py-1.5 text-sm focus:border-blue-500 focus:bg-white focus:outline-none"
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
          />
          {isSubmitting && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Spinner size="sm" />
            </div>
          )}
        </div>
      </form>
    </div>
  );
}
