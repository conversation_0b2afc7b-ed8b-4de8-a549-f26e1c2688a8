"use client";

// Post card hook - Updated to fix validation errors with null values
import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import eventBus from "@/lib/eventBus";
import { Post } from "@/types/post";
import { useToast } from "@/contexts/ToastContext";
import { useThrottle } from "@/hooks/useDebounce";

interface UsePostCardProps {
  post: Post;
  onLike: () => void;
  onDislike: () => void;
}

export function usePostCard({ post, onLike, onDislike }: UsePostCardProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const { showError } = useToast();
  
  // State management
  const [showComments, setShowComments] = useState(false);
  const [commentCount, setCommentCount] = useState(post._count.comments);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [editContent, setEditContent] = useState(post.content);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showSeeMore, setShowSeeMore] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLikeAnimating, setIsLikeAnimating] = useState(false);
  const [isDislikeAnimating, setIsDislikeAnimating] = useState(false);
  const [imageLoadError, setImageLoadError] = useState<string[]>([]);
  const [isHovered, setIsHovered] = useState(false);

  // Computed values
  const isCurrentUserPost = useMemo(() => 
    post.type === 'user_post' && session?.user?.id === post.user?.id,
    [post.type, post.user?.id, session?.user?.id]
  );

  const isFanPagePost = useMemo(() => post.type === 'fan_page_post', [post.type]);
  const isGroupPost = useMemo(() => post.type === 'group_post', [post.type]);

  // Get the display info (user, fan page, or group)
  const displayInfo = useMemo(() => {
    if (isFanPagePost) {
      return {
        id: post.fanPage?.id || '',
        name: post.fanPage?.name || '',
        username: post.fanPage?.username || '',
        image: post.fanPage?.profileImage || null,
        isVerified: post.fanPage?.isVerified || false,
        profileUrl: `/pages/${post.fanPage?.username || post.fanPage?.id}`,
        subtitle: null
      };
    }
    
    if (isGroupPost) {
      return {
        id: post.user?.id || '',
        name: post.user?.name || '',
        username: post.user?.username || '',
        image: post.user?.image || null,
        isVerified: false,
        profileUrl: `/user/${post.user?.username || post.user?.id}`,
        subtitle: post.group?.name || ''
      };
    }
    
    return {
      id: post.user?.id || '',
      name: post.user?.name || '',
      username: post.user?.username || '',
      image: post.user?.image || null,
      isVerified: false,
      profileUrl: `/user/${post.user?.username || post.user?.id}`,
      subtitle: null
    };
  }, [isFanPagePost, isGroupPost, post.fanPage, post.user, post.group]);

  // Internal like handler
  const internalHandleLike = useCallback(async () => {
    if (isLikeAnimating || isDislikeAnimating) return;

    setIsLikeAnimating(true);
    setError(null);

    try {
      await onLike();
    } catch (error) {
      console.error('Error liking post:', error);
      const errorMessage = 'Failed to like post. Please try again.';
      setError(errorMessage);
      showError('Like Failed', errorMessage);
    } finally {
      // Use a more consistent animation duration
      setTimeout(() => setIsLikeAnimating(false), 250);
    }
  }, [onLike, isLikeAnimating, isDislikeAnimating, showError]);

  // Internal dislike handler
  const internalHandleDislike = useCallback(async () => {
    if (isDislikeAnimating || isLikeAnimating) return;

    setIsDislikeAnimating(true);
    setError(null);

    try {
      await onDislike();
    } catch (error) {
      console.error('Error disliking post:', error);
      const errorMessage = 'Failed to dislike post. Please try again.';
      setError(errorMessage);
      showError('Dislike Failed', errorMessage);
    } finally {
      // Use a more consistent animation duration
      setTimeout(() => setIsDislikeAnimating(false), 250);
    }
  }, [onDislike, isDislikeAnimating, isLikeAnimating, showError]);

  // Throttled handlers to prevent rapid clicking
  const handleLike = useThrottle(internalHandleLike, 500);
  const handleDislike = useThrottle(internalHandleDislike, 500);

  // Handle bookmark toggle
  const handleBookmark = useCallback(async () => {
    try {
      const response = await fetch(`/api/posts/${post.id}/bookmark`, {
        method: isBookmarked ? 'DELETE' : 'POST',
      });
      
      if (response.ok) {
        setIsBookmarked(!isBookmarked);
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
    }
  }, [post.id, isBookmarked]);

  // Handle image load error
  const handleImageError = useCallback((imageSrc: string) => {
    setImageLoadError(prev => [...prev, imageSrc]);
  }, []);

  // Reset edit content when post changes or modal opens
  useEffect(() => {
    if (isEditModalOpen) {
      setEditContent(post.content);
      setError(null);
    }
  }, [isEditModalOpen, post.content]);

  // Check if content needs "See More" functionality
  useEffect(() => {
    const contentLength = post.content?.length || 0;
    setShowSeeMore(contentLength > 300);
  }, [post.content]);

  // Listen for comment-added events for this post
  useEffect(() => {
    const handleCommentAdded = (postId: string) => {
      if (postId === post.id) {
        setCommentCount(prev => prev + 1);
      }
    };

    eventBus.on('comment-added', handleCommentAdded);
    return () => {
      eventBus.off('comment-added', handleCommentAdded);
    };
  }, [post.id]);

  const handleEditPost = useCallback(() => {
    setIsEditModalOpen(true);
  }, []);

  const handleSaveEdit = useCallback(async () => {
    if (!editContent.trim()) {
      setError("Post content cannot be empty");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const apiUrl = isFanPagePost
        ? `/api/fan-pages/posts/${post.id}`
        : isGroupPost
        ? `/api/groups/posts/${post.id}`
        : `/api/posts/${post.id}`;

      // Prepare the request body - only send fields that are actually needed
      // Create a clean object with only the content field initially
      const requestBody: Record<string, any> = {};

      // Always include content
      requestBody.content = editContent;

      // For fan page posts, include additional fields if they exist and are not null/undefined
      if (isFanPagePost) {
        if (post.images && Array.isArray(post.images) && post.images.length > 0) {
          requestBody.images = post.images;
        }
        if (post.videos && Array.isArray(post.videos) && post.videos.length > 0) {
          requestBody.videos = post.videos;
        }
        if (post.type && post.type !== null && post.type !== undefined) {
          requestBody.type = post.type;
        }
      }

      // Always log for debugging this specific issue
      console.log('🔍 POST UPDATE DEBUG:');
      console.log('API URL:', apiUrl);
      console.log('Post type - isFanPagePost:', isFanPagePost, 'isGroupPost:', isGroupPost);
      console.log('Original post data:', {
        backgroundColor: post.backgroundColor,
        feeling: post.feeling,
        activity: post.activity,
        location: post.location,
        formattedContent: post.formattedContent
      });
      console.log('Request body being sent:', JSON.stringify(requestBody, null, 2));

      const response = await fetch(apiUrl, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        let errorMessage = "Failed to update post";
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
            // If there are detailed validation errors, append them
            if (errorData.errors && Array.isArray(errorData.errors)) {
              const validationDetails = errorData.errors
                .map((err: any) => `${err.field || 'Field'}: ${err.message}`)
                .join(", ");
              errorMessage += ` (${validationDetails})`;
            }
          } else if (errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData.errors && Array.isArray(errorData.errors)) {
            // Handle validation errors
            errorMessage = errorData.errors.map((err: any) => err.message || err).join(", ");
          }
        } catch {
          // If JSON parsing fails, use status-based error message
          switch (response.status) {
            case 400:
              errorMessage = "Invalid post data. Please check your content.";
              break;
            case 401:
              errorMessage = "You are not authorized to edit this post.";
              break;
            case 403:
              errorMessage = "You don't have permission to edit this post.";
              break;
            case 404:
              errorMessage = "Post not found.";
              break;
            case 500:
              errorMessage = "Server error. Please try again later.";
              break;
            default:
              errorMessage = `Failed to update post (${response.status})`;
          }
        }
        throw new Error(errorMessage);
      }

      setIsEditModalOpen(false);
      router.refresh();
      eventBus.emit('post-updated', post.id);
    } catch (error) {
      console.error("Error updating post:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update post. Please try again.";
      setError(errorMessage);
      showError('Update Failed', errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }, [editContent, post, isFanPagePost, isGroupPost, router, showError]);

  const handleDeletePost = useCallback(() => {
    setIsDeleteModalOpen(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    try {
      setIsSubmitting(true);

      const apiUrl = isFanPagePost
        ? `/api/fan-pages/posts/${post.id}`
        : isGroupPost
        ? `/api/groups/posts/${post.id}`
        : `/api/posts/${post.id}`;

      const response = await fetch(apiUrl, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete post");
      }

      // Parse response to get media deletion info
      const result = await response.json();

      setIsDeleteModalOpen(false);
      router.refresh();
      eventBus.emit('post-deleted', post.id);

      // Show success message with media deletion info
      let successMessage = 'Post deleted successfully';
      if (result.mediaDeleted) {
        if (result.mediaDeleted.deletedCount > 0) {
          successMessage += ` (${result.mediaDeleted.deletedCount} media files removed)`;
        }
        if (result.mediaDeleted.failedCount > 0) {
          successMessage += ` (${result.mediaDeleted.failedCount} media files could not be removed)`;
        }
      }

      showError && showError('Success', successMessage);
    } catch (error) {
      console.error("Error deleting post:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete post. Please try again.";
      showError && showError('Delete Failed', errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }, [post.id, isFanPagePost, isGroupPost, router, showError]);

  const handleCancelDelete = useCallback(() => {
    setIsDeleteModalOpen(false);
  }, []);

  return {
    // State
    showComments,
    setShowComments,
    commentCount,
    isEditModalOpen,
    setIsEditModalOpen,
    isShareModalOpen,
    setIsShareModalOpen,
    isDeleteModalOpen,
    setIsDeleteModalOpen,
    editContent,
    setEditContent,
    isSubmitting,
    error,
    isExpanded,
    setIsExpanded,
    showSeeMore,
    isBookmarked,
    isLikeAnimating,
    isDislikeAnimating,
    imageLoadError,
    isHovered,
    setIsHovered,

    // Computed values
    isCurrentUserPost,
    isFanPagePost,
    isGroupPost,
    displayInfo,

    // Handlers
    handleLike,
    handleDislike,
    handleBookmark,
    handleImageError,
    handleEditPost,
    handleSaveEdit,
    handleDeletePost,
    handleConfirmDelete,
    handleCancelDelete,
  };
}
