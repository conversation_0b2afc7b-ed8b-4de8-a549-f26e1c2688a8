import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { WalletDashboard } from "@/components/wallet/WalletDashboard";

export const dynamic = 'force-dynamic';

export default async function WalletPage() {
  const user = await requireAuth();

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Wallet</h1>
          <p className="text-gray-600 mt-2">
            Manage your funds, send money, and track transactions
          </p>
        </div>

        <WalletDashboard userId={user.id} />
      </div>
    </MainLayout>
  );
}
