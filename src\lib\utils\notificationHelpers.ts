/**
 * Notification Helper Functions
 * Client-safe utilities for notification display and formatting
 */

export interface CreateNotificationData {
  recipientId: string;
  type: string;
  senderId?: string;
  postId?: string;
  commentId?: string;
  messageId?: string;
  groupId?: string;
  eventId?: string;
  fanPageId?: string;
  subscriptionId?: string;
  data?: any; // Additional data for real-time notification
}

// Note: Database operations have been moved to server-side API routes
// Use /api/notifications/create and /api/notifications/bulk-create endpoints instead

/**
 * Get notification type display text
 */
export function getNotificationDisplayText(type: string): string {
  const displayTexts: Record<string, string> = {
    like: 'liked your post',
    comment: 'commented on your post',
    message: 'sent you a message',
    group_invite: 'invited you to join a group',
    group_join_request: 'requested to join your group',
    group_join_approved: 'approved your group join request',
    group_post: 'posted in your group',
    group_announcement: 'made an announcement in your group',
    event_invite: 'invited you to an event',
    event_reminder: 'event reminder',
    event_update: 'updated an event',
    event_comment: 'commented on your event',
    store_follow: 'started following your store',
    store_review: 'reviewed your store',
    product_new: 'added a new product',
    product_report: 'reported a product',
    fan_page_follow: 'started following your page',
    fan_page_post: 'posted on your page',
    fan_page_comment: 'commented on your page',
    fan_page_like: 'liked your page',
    fan_page_role_added: 'added you as a page admin',
    fan_page_message: 'sent a message to your page',
    fan_page_reply: 'replied to your message',
    subscription: 'subscribed to your updates',
    subscription_back: 'subscribed back to you',
  };

  return displayTexts[type] || 'sent you a notification';
}

/**
 * Get notification icon based on type
 */
export function getNotificationIcon(type: string): string {
  const icons: Record<string, string> = {
    like: '❤️',
    comment: '💬',
    message: '📩',
    group_invite: '👥',
    group_join_request: '🔔',
    group_join_approved: '✅',
    group_post: '📝',
    group_announcement: '📢',
    event_invite: '📅',
    event_reminder: '⏰',
    event_update: '📝',
    event_comment: '💬',
    store_follow: '🏪',
    store_review: '⭐',
    product_new: '🆕',
    product_report: '⚠️',
    fan_page_follow: '👍',
    fan_page_post: '📝',
    fan_page_comment: '💬',
    fan_page_like: '❤️',
    fan_page_role_added: '👑',
    fan_page_message: '📩',
    fan_page_reply: '↩️',
    subscription: '🔔',
    subscription_back: '🔄',
  };

  return icons[type] || '🔔';
}

/**
 * Check if notification type should show browser notification
 */
export function shouldShowBrowserNotification(type: string): boolean {
  const browserNotificationTypes = [
    'message',
    'comment',
    'like',
    'group_invite',
    'event_invite',
    'fan_page_message',
    'fan_page_reply',
  ];

  return browserNotificationTypes.includes(type);
}
