/**
 * Notification Helper Functions
 * Utilities for creating and broadcasting notifications with real-time support
 */

import { db } from "@/lib/db";
import { notifications } from "@/lib/db/schema";
import { getSupabaseServiceClient } from "@/lib/supabase/client";
import { v4 as uuidv4 } from "uuid";

export interface CreateNotificationData {
  recipientId: string;
  type: string;
  senderId?: string;
  postId?: string;
  commentId?: string;
  messageId?: string;
  groupId?: string;
  eventId?: string;
  fanPageId?: string;
  subscriptionId?: string;
  data?: any; // Additional data for real-time notification
}

/**
 * Create a notification in MySQL and broadcast it via Supabase real-time
 */
export async function createNotification(notificationData: CreateNotificationData): Promise<string> {
  const notificationId = uuidv4();

  try {
    // Insert notification into MySQL (primary storage)
    await db.insert(notifications).values({
      id: notificationId,
      recipientId: notificationData.recipientId,
      type: notificationData.type as any,
      senderId: notificationData.senderId,
      postId: notificationData.postId,
      commentId: notificationData.commentId,
      messageId: notificationData.messageId,
      groupId: notificationData.groupId,
      eventId: notificationData.eventId,
      fanPageId: notificationData.fanPageId,
      subscriptionId: notificationData.subscriptionId,
      read: false,
    });

    // Broadcast via Supabase real-time
    try {
      const supabase = getSupabaseServiceClient();
      await supabase.from('notifications_realtime').insert({
        mysql_notification_id: notificationId,
        recipient_id: notificationData.recipientId,
        type: notificationData.type,
        data: notificationData.data || {},
      });
    } catch (realtimeError) {
      console.error('Real-time notification broadcast failed:', realtimeError);
      // Don't fail the main operation if real-time fails
    }

    return notificationId;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Create multiple notifications efficiently
 */
export async function createBulkNotifications(
  notifications: CreateNotificationData[]
): Promise<string[]> {
  const notificationIds: string[] = [];

  try {
    // Prepare MySQL data
    const mysqlData = notifications.map((notif) => {
      const id = uuidv4();
      notificationIds.push(id);
      return {
        id,
        recipientId: notif.recipientId,
        type: notif.type as any,
        senderId: notif.senderId,
        postId: notif.postId,
        commentId: notif.commentId,
        messageId: notif.messageId,
        groupId: notif.groupId,
        eventId: notif.eventId,
        fanPageId: notif.fanPageId,
        subscriptionId: notif.subscriptionId,
        read: false,
      };
    });

    // Insert into MySQL
    await db.insert(notifications).values(mysqlData);

    // Prepare Supabase data
    const supabaseData = notifications.map((notif, index) => ({
      mysql_notification_id: notificationIds[index],
      recipient_id: notif.recipientId,
      type: notif.type,
      data: notif.data || {},
    }));

    // Broadcast via Supabase real-time
    try {
      const supabase = getSupabaseServiceClient();
      await supabase.from('notifications_realtime').insert(supabaseData);
    } catch (realtimeError) {
      console.error('Real-time bulk notification broadcast failed:', realtimeError);
      // Don't fail the main operation if real-time fails
    }

    return notificationIds;
  } catch (error) {
    console.error('Error creating bulk notifications:', error);
    throw error;
  }
}

/**
 * Get notification type display text
 */
export function getNotificationDisplayText(type: string): string {
  const displayTexts: Record<string, string> = {
    like: 'liked your post',
    comment: 'commented on your post',
    message: 'sent you a message',
    group_invite: 'invited you to join a group',
    group_join_request: 'requested to join your group',
    group_join_approved: 'approved your group join request',
    group_post: 'posted in your group',
    group_announcement: 'made an announcement in your group',
    event_invite: 'invited you to an event',
    event_reminder: 'event reminder',
    event_update: 'updated an event',
    event_comment: 'commented on your event',
    store_follow: 'started following your store',
    store_review: 'reviewed your store',
    product_new: 'added a new product',
    product_report: 'reported a product',
    fan_page_follow: 'started following your page',
    fan_page_post: 'posted on your page',
    fan_page_comment: 'commented on your page',
    fan_page_like: 'liked your page',
    fan_page_role_added: 'added you as a page admin',
    fan_page_message: 'sent a message to your page',
    fan_page_reply: 'replied to your message',
    subscription: 'subscribed to your updates',
    subscription_back: 'subscribed back to you',
  };

  return displayTexts[type] || 'sent you a notification';
}

/**
 * Get notification icon based on type
 */
export function getNotificationIcon(type: string): string {
  const icons: Record<string, string> = {
    like: '❤️',
    comment: '💬',
    message: '📩',
    group_invite: '👥',
    group_join_request: '🔔',
    group_join_approved: '✅',
    group_post: '📝',
    group_announcement: '📢',
    event_invite: '📅',
    event_reminder: '⏰',
    event_update: '📝',
    event_comment: '💬',
    store_follow: '🏪',
    store_review: '⭐',
    product_new: '🆕',
    product_report: '⚠️',
    fan_page_follow: '👍',
    fan_page_post: '📝',
    fan_page_comment: '💬',
    fan_page_like: '❤️',
    fan_page_role_added: '👑',
    fan_page_message: '📩',
    fan_page_reply: '↩️',
    subscription: '🔔',
    subscription_back: '🔄',
  };

  return icons[type] || '🔔';
}

/**
 * Check if notification type should show browser notification
 */
export function shouldShowBrowserNotification(type: string): boolean {
  const browserNotificationTypes = [
    'message',
    'comment',
    'like',
    'group_invite',
    'event_invite',
    'fan_page_message',
    'fan_page_reply',
  ];

  return browserNotificationTypes.includes(type);
}
