/**
 * Real-time System Test Script
 * Tests all real-time functionality including messaging, notifications, and presence
 */

import { getSupabaseServiceClient } from '../lib/supabase/client';
import { hybridSyncService } from '../lib/services/hybridSyncService';

interface TestResult {
  name: string;
  success: boolean;
  message: string;
  duration: number;
}

class RealtimeSystemTester {
  private supabase = getSupabaseServiceClient();
  private results: TestResult[] = [];

  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting Real-time System Tests...');
    console.log('=====================================');

    this.results = [];

    // Test Supabase connection
    await this.testSupabaseConnection();

    // Test database tables
    await this.testDatabaseTables();

    // Test real-time subscriptions
    await this.testRealtimeSubscriptions();

    // Test message functionality
    await this.testMessageFunctionality();

    // Test notification functionality
    await this.testNotificationFunctionality();

    // Test presence functionality
    await this.testPresenceFunctionality();

    // Test sync functionality
    await this.testSyncFunctionality();

    // Test cleanup functionality
    await this.testCleanupFunctionality();

    // Print results
    this.printResults();

    return this.results;
  }

  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        success: true,
        message: 'Passed',
        duration,
      });
      console.log(`✅ ${name} - ${duration}ms`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
        duration,
      });
      console.log(`❌ ${name} - ${error}`);
    }
  }

  private async testSupabaseConnection(): Promise<void> {
    await this.runTest('Supabase Connection', async () => {
      const { error } = await this.supabase.from('user_presence').select('count').limit(1);
      if (error) throw new Error(`Connection failed: ${error.message}`);
    });
  }

  private async testDatabaseTables(): Promise<void> {
    await this.runTest('Database Tables', async () => {
      // Test messages_realtime table
      const { error: messagesError } = await this.supabase
        .from('messages_realtime')
        .select('count')
        .limit(1);
      if (messagesError) throw new Error(`messages_realtime table error: ${messagesError.message}`);

      // Test notifications_realtime table
      const { error: notificationsError } = await this.supabase
        .from('notifications_realtime')
        .select('count')
        .limit(1);
      if (notificationsError) throw new Error(`notifications_realtime table error: ${notificationsError.message}`);

      // Test user_presence table
      const { error: presenceError } = await this.supabase
        .from('user_presence')
        .select('count')
        .limit(1);
      if (presenceError) throw new Error(`user_presence table error: ${presenceError.message}`);
    });
  }

  private async testRealtimeSubscriptions(): Promise<void> {
    await this.runTest('Real-time Subscriptions', async () => {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Subscription timeout'));
        }, 5000);

        const channel = this.supabase
          .channel('test-channel')
          .on('postgres_changes', { event: '*', schema: 'public', table: 'user_presence' }, () => {
            clearTimeout(timeout);
            this.supabase.removeChannel(channel);
            resolve();
          })
          .subscribe((status) => {
            if (status === 'SUBSCRIBED') {
              // Trigger a change to test subscription
              this.supabase.rpc('update_user_presence', {
                p_user_id: 'test-user',
                p_status: 'online',
              });
            }
          });
      });
    });
  }

  private async testMessageFunctionality(): Promise<void> {
    await this.runTest('Message Functionality', async () => {
      const testMessageId = `test-msg-${Date.now()}`;
      
      // Insert test message
      const { error: insertError } = await this.supabase
        .from('messages_realtime')
        .insert({
          mysql_message_id: testMessageId,
          sender_id: 'test-sender',
          receiver_id: 'test-receiver',
          content: 'Test message content',
        });
      
      if (insertError) throw new Error(`Insert failed: ${insertError.message}`);

      // Verify message exists
      const { data, error: selectError } = await this.supabase
        .from('messages_realtime')
        .select('*')
        .eq('mysql_message_id', testMessageId)
        .single();

      if (selectError) throw new Error(`Select failed: ${selectError.message}`);
      if (!data) throw new Error('Message not found after insert');

      // Update message as read
      const { error: updateError } = await this.supabase
        .from('messages_realtime')
        .update({ read_at: new Date().toISOString() })
        .eq('mysql_message_id', testMessageId);

      if (updateError) throw new Error(`Update failed: ${updateError.message}`);

      // Cleanup
      await this.supabase
        .from('messages_realtime')
        .delete()
        .eq('mysql_message_id', testMessageId);
    });
  }

  private async testNotificationFunctionality(): Promise<void> {
    await this.runTest('Notification Functionality', async () => {
      const testNotificationId = `test-notif-${Date.now()}`;
      
      // Insert test notification
      const { error: insertError } = await this.supabase
        .from('notifications_realtime')
        .insert({
          mysql_notification_id: testNotificationId,
          recipient_id: 'test-recipient',
          type: 'test',
          data: { test: true },
        });
      
      if (insertError) throw new Error(`Insert failed: ${insertError.message}`);

      // Verify notification exists
      const { data, error: selectError } = await this.supabase
        .from('notifications_realtime')
        .select('*')
        .eq('mysql_notification_id', testNotificationId)
        .single();

      if (selectError) throw new Error(`Select failed: ${selectError.message}`);
      if (!data) throw new Error('Notification not found after insert');

      // Cleanup
      await this.supabase
        .from('notifications_realtime')
        .delete()
        .eq('mysql_notification_id', testNotificationId);
    });
  }

  private async testPresenceFunctionality(): Promise<void> {
    await this.runTest('Presence Functionality', async () => {
      const testUserId = `test-user-${Date.now()}`;
      
      // Update user presence
      const { error: updateError } = await this.supabase.rpc('update_user_presence', {
        p_user_id: testUserId,
        p_status: 'online',
      });
      
      if (updateError) throw new Error(`Presence update failed: ${updateError.message}`);

      // Verify presence exists
      const { data, error: selectError } = await this.supabase
        .from('user_presence')
        .select('*')
        .eq('user_id', testUserId)
        .single();

      if (selectError) throw new Error(`Select failed: ${selectError.message}`);
      if (!data) throw new Error('Presence not found after update');
      if (data.status !== 'online') throw new Error('Presence status not updated correctly');

      // Cleanup
      await this.supabase
        .from('user_presence')
        .delete()
        .eq('user_id', testUserId);
    });
  }

  private async testSyncFunctionality(): Promise<void> {
    await this.runTest('Sync Functionality', async () => {
      const health = await hybridSyncService.healthCheck();
      if (!health.healthy) {
        throw new Error(`Sync health check failed: ${health.issues.join(', ')}`);
      }
    });
  }

  private async testCleanupFunctionality(): Promise<void> {
    await this.runTest('Cleanup Functionality', async () => {
      // Insert old test data
      const oldDate = new Date(Date.now() - 8 * 24 * 60 * 60 * 1000); // 8 days ago
      
      const { error: insertError } = await this.supabase
        .from('messages_realtime')
        .insert({
          mysql_message_id: 'old-test-message',
          sender_id: 'test-sender',
          receiver_id: 'test-receiver',
          content: 'Old test message',
          created_at: oldDate.toISOString(),
        });

      if (insertError) throw new Error(`Insert old data failed: ${insertError.message}`);

      // Run cleanup
      const cleanup = await hybridSyncService.cleanupOldData();
      
      if (cleanup.errors.length > 0) {
        throw new Error(`Cleanup errors: ${cleanup.errors.join(', ')}`);
      }

      // Verify old data was cleaned up
      const { data } = await this.supabase
        .from('messages_realtime')
        .select('*')
        .eq('mysql_message_id', 'old-test-message');

      if (data && data.length > 0) {
        throw new Error('Old data was not cleaned up');
      }
    });
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('=====================================');
    
    const passed = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => r.success === false).length;
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏱️  Total Time: ${totalTime}ms`);
    console.log(`📈 Success Rate: ${Math.round((passed / this.results.length) * 100)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => !r.success)
        .forEach(r => console.log(`  - ${r.name}: ${r.message}`));
    }

    console.log('\n🎉 Real-time system testing completed!');
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new RealtimeSystemTester();
  tester.runAllTests().catch(console.error);
}

export { RealtimeSystemTester };
