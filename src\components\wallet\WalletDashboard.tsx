"use client";

import { useState, useEffect, useRef } from "react";
import { useSearchParams } from "next/navigation";
import { WalletCard, WalletActions } from "@/components/ui/WalletCard";
import { TransactionList } from "@/components/ui/TransactionCard";
import { Button } from "@/components/ui/Button";
import { DepositModal } from "./DepositModal";
import { SendMoneyModal } from "./SendMoneyModal";
import { CashoutModal } from "./CashoutModal";
import { InternalTransferModal } from "./InternalTransferModal";
import { WithdrawModal } from "./WithdrawModal";
import { PinSetupModal } from "./PinSetupModal";
import {
  ClockIcon,
  Cog6ToothIcon,
  ExclamationTriangleIcon,
  ShieldCheckIcon
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";

interface WalletDashboardProps {
  userId: string;
}

interface WalletBalance {
  generalBalance: string;
  earningBalance: string;
  totalDeposited: string;
  totalWithdrawn: string;
  totalSent: string;
  totalReceived: string;
}

interface Transaction {
  id: string;
  type: 'deposit' | 'send' | 'receive' | 'cashout' | 'internal_transfer' | 'earning' | 'withdraw';
  amount: string;
  fee: string;
  netAmount: string;
  walletType: 'general' | 'earning';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  paymentGateway?: string;
  reference?: string;
  note?: string;
  createdAt: string;
  toUser?: {
    id: string;
    name: string;
    username: string;
    image?: string;
  };
  toAgent?: {
    id: string;
    name: string;
    serviceType: string;
  };
  fromWalletType?: 'general' | 'earning';
  toWalletType?: 'general' | 'earning';
}

export function WalletDashboard({ userId }: WalletDashboardProps) {
  const searchParams = useSearchParams();
  const [balance, setBalance] = useState<WalletBalance | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [transactionsLoading, setTransactionsLoading] = useState(true);
  const [hasPin, setHasPin] = useState(false);
  const [pinStatus, setPinStatus] = useState<any>(null);

  // Modal states
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [showSendModal, setShowSendModal] = useState(false);
  const [showCashoutModal, setShowCashoutModal] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showPinSetupModal, setShowPinSetupModal] = useState(false);

  // Use ref to prevent duplicate toast messages
  const withdrawMessageShownRef = useRef(false);

  // Fetch wallet balance
  const fetchBalance = async () => {
    try {
      const response = await fetch('/api/wallet/balance');
      const data = await response.json();

      if (data.success) {
        setBalance(data.data);
      } else {
        toast.error('Failed to fetch wallet balance');
      }
    } catch (error) {
      console.error('Error fetching balance:', error);
      toast.error('Failed to fetch wallet balance');
    } finally {
      setLoading(false);
    }
  };

  // Fetch recent transactions
  const fetchTransactions = async () => {
    try {
      const response = await fetch('/api/wallet/transactions?limit=10');
      const data = await response.json();

      if (data.success) {
        setTransactions(data.data.transactions);
      } else {
        toast.error('Failed to fetch transactions');
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast.error('Failed to fetch transactions');
    } finally {
      setTransactionsLoading(false);
    }
  };

  // Check PIN status
  const checkPinStatus = async () => {
    try {
      const response = await fetch('/api/wallet/pin');
      const data = await response.json();

      if (data.success) {
        setHasPin(data.data.hasPin);
        setPinStatus(data.data);
      }
    } catch (error) {
      console.error('Error checking PIN status:', error);
    }
  };

  useEffect(() => {
    fetchBalance();
    fetchTransactions();
    checkPinStatus();
  }, []);

  // Check for URL parameters to show withdraw modal
  useEffect(() => {
    const action = searchParams.get('action');
    if (action === 'withdraw' && !withdrawMessageShownRef.current) {
      withdrawMessageShownRef.current = true;
      toast.success('আপনার earning wallet থেকে টাকা withdraw করুন');
      setShowWithdrawModal(true);
    }
  }, [searchParams]);

  // Refresh data after successful operations
  const refreshData = () => {
    fetchBalance();
    fetchTransactions();
  };

  const handleActionClick = (action: string) => {
    if (!hasPin) {
      setShowPinSetupModal(true);
      return;
    }

    switch (action) {
      case 'deposit':
        setShowDepositModal(true);
        break;
      case 'send':
        setShowSendModal(true);
        break;
      case 'cashout':
        setShowCashoutModal(true);
        break;
      case 'transfer':
        setShowTransferModal(true);
        break;
      case 'withdraw':
        setShowWithdrawModal(true);
        break;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Loading skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-48 bg-gray-200 rounded-xl animate-pulse" />
          <div className="h-48 bg-gray-200 rounded-xl animate-pulse" />
        </div>
        <div className="h-64 bg-gray-200 rounded-lg animate-pulse" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* PIN Setup Warning */}
      {!hasPin && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-3" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-yellow-800">
                Wallet PIN Required
              </h3>
              <p className="text-sm text-yellow-700 mt-1">
                Set up a PIN to secure your wallet transactions and enable all features.
              </p>
            </div>
            <Button
              size="sm"
              onClick={() => setShowPinSetupModal(true)}
              className="ml-4"
            >
              Set PIN
            </Button>
          </div>
        </div>
      )}

      {/* PIN Locked Warning */}
      {pinStatus?.isLocked && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <ShieldCheckIcon className="h-5 w-5 text-red-600 mr-3" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-800">
                Wallet Temporarily Locked
              </h3>
              <p className="text-sm text-red-700 mt-1">
                Your wallet is locked due to multiple failed PIN attempts. Please try again later.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Wallet Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Wallet */}
        <WalletCard
          title="General Wallet"
          balance={balance?.generalBalance || "0.00"}
          type="general"
          actions={
            <>
              <WalletActions.Deposit onClick={() => handleActionClick('deposit')} />
              <WalletActions.Send onClick={() => handleActionClick('send')} />
              <WalletActions.Cashout onClick={() => handleActionClick('cashout')} />
            </>
          }
        />

        {/* Earning Wallet */}
        <WalletCard
          title="Earning Wallet"
          balance={balance?.earningBalance || "0.00"}
          type="earning"
          actions={
            <>
              <WalletActions.Withdraw onClick={() => handleActionClick('withdraw')} />
              <WalletActions.Transfer onClick={() => handleActionClick('transfer')} />
            </>
          }
        />
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-sm text-gray-600">Total Deposited</div>
          <div className="text-xl font-semibold text-green-600">
            ${parseFloat(balance?.totalDeposited || "0").toFixed(2)}
          </div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-sm text-gray-600">Total Sent</div>
          <div className="text-xl font-semibold text-red-600">
            ${parseFloat(balance?.totalSent || "0").toFixed(2)}
          </div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-sm text-gray-600">Total Received</div>
          <div className="text-xl font-semibold text-green-600">
            ${parseFloat(balance?.totalReceived || "0").toFixed(2)}
          </div>
        </div>
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="text-sm text-gray-600">Total Withdrawn</div>
          <div className="text-xl font-semibold text-orange-600">
            ${parseFloat(balance?.totalWithdrawn || "0").toFixed(2)}
          </div>
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Recent Transactions</h2>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = '/wallet/transactions'}
            >
              <ClockIcon className="h-4 w-4 mr-1" />
              View All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = '/wallet/settings'}
            >
              <Cog6ToothIcon className="h-4 w-4 mr-1" />
              Settings
            </Button>
          </div>
        </div>

        <TransactionList
          transactions={transactions}
          loading={transactionsLoading}
          emptyMessage="No transactions yet. Start by depositing funds or receiving money."
        />
      </div>

      {/* Modals */}
      <DepositModal
        isOpen={showDepositModal}
        onClose={() => setShowDepositModal(false)}
        onSuccess={refreshData}
      />

      <SendMoneyModal
        isOpen={showSendModal}
        onClose={() => setShowSendModal(false)}
        onSuccess={refreshData}
      />

      <CashoutModal
        isOpen={showCashoutModal}
        onClose={() => setShowCashoutModal(false)}
        onSuccess={refreshData}
      />

      <InternalTransferModal
        isOpen={showTransferModal}
        onClose={() => setShowTransferModal(false)}
        onSuccess={refreshData}
        earningBalance={balance?.earningBalance || "0.00"}
      />

      <WithdrawModal
        isOpen={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
        onSuccess={refreshData}
        earningBalance={balance?.earningBalance || "0.00"}
      />

      <PinSetupModal
        isOpen={showPinSetupModal}
        onClose={() => setShowPinSetupModal(false)}
        onSuccess={() => {
          setHasPin(true);
          checkPinStatus();
          toast.success('PIN set successfully!');
        }}
      />
    </div>
  );
}
