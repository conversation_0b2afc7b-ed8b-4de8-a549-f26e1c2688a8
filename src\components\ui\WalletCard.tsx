"use client";

import { cn } from "@/lib/utils";
import {
  WalletIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";

interface WalletCardProps {
  title: string;
  balance: string;
  currency?: string;
  type: 'general' | 'earning';
  actions?: React.ReactNode;
  className?: string;
}

export function WalletCard({
  title,
  balance,
  currency = "USD",
  type,
  actions,
  className
}: WalletCardProps) {
  const isGeneral = type === 'general';

  return (
    <div className={cn(
      "relative overflow-hidden rounded-xl p-6 text-white",
      isGeneral
        ? "bg-gradient-to-br from-blue-600 to-blue-800"
        : "bg-gradient-to-br from-green-600 to-green-800",
      className
    )}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute -right-4 -top-4 h-24 w-24 rounded-full bg-white/20" />
        <div className="absolute -bottom-6 -left-6 h-32 w-32 rounded-full bg-white/10" />
      </div>

      {/* Content */}
      <div className="relative">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <WalletIcon className="h-6 w-6" />
            <h3 className="text-lg font-semibold">{title}</h3>
          </div>
          {isGeneral && (
            <div className="flex items-center space-x-1 text-xs bg-white/20 px-2 py-1 rounded-full">
              <span>Primary</span>
            </div>
          )}
        </div>

        <div className="mb-6">
          <div className="text-sm opacity-80 mb-1">Available Balance</div>
          <div className="text-3xl font-bold">
            {currency === 'USD' ? '$' : currency + ' '}
            {parseFloat(balance).toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            })}
          </div>
        </div>

        {actions && (
          <div className="flex flex-wrap gap-2">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
}

interface WalletActionButtonProps {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  disabled?: boolean;
}

export function WalletActionButton({
  icon,
  label,
  onClick,
  variant = 'primary',
  disabled = false
}: WalletActionButtonProps) {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors",
        variant === 'primary'
          ? "bg-white/20 hover:bg-white/30 text-white"
          : variant === 'outline'
          ? "border border-white/30 text-white hover:bg-white/10"
          : "bg-white text-gray-900 hover:bg-gray-100",
        disabled && "opacity-50 cursor-not-allowed"
      )}
    >
      {icon}
      <span>{label}</span>
    </button>
  );
}

// Quick action buttons for wallet cards
export const WalletActions = {
  Deposit: ({ onClick }: { onClick: () => void }) => (
    <WalletActionButton
      icon={<PlusIcon className="h-4 w-4" />}
      label="Deposit"
      onClick={onClick}
    />
  ),

  Send: ({ onClick }: { onClick: () => void }) => (
    <WalletActionButton
      icon={<ArrowUpIcon className="h-4 w-4" />}
      label="Send"
      onClick={onClick}
    />
  ),

  Cashout: ({ onClick }: { onClick: () => void }) => (
    <WalletActionButton
      icon={<ArrowDownIcon className="h-4 w-4" />}
      label="Cashout"
      onClick={onClick}
    />
  ),

  Transfer: ({ onClick }: { onClick: () => void }) => (
    <WalletActionButton
      icon={<ArrowRightIcon className="h-4 w-4" />}
      label="Transfer"
      onClick={onClick}
    />
  ),

  Withdraw: ({ onClick }: { onClick: () => void }) => (
    <WalletActionButton
      icon={<ArrowDownIcon className="h-4 w-4" />}
      label="Withdraw"
      onClick={onClick}
      variant="outline"
    />
  ),
};
