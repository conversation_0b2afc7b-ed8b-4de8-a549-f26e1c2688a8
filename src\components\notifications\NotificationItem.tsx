"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { formatDistanceToNow } from 'date-fns';
import {
  HeartIcon,
  ChatBubbleLeftIcon,
  UserPlusIcon,
  CheckIcon,
  EnvelopeIcon,
  UsersIcon,
  CalendarIcon,
  ShoppingBagIcon,
  StarIcon,
  XMarkIcon,
  EllipsisHorizontalIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { NotificationData } from '@/hooks/useNotifications';
import { NotificationTime } from '@/components/ui/TimeDisplay';
import { cn } from '@/lib/utils';
import { Menu } from '@headlessui/react';

interface NotificationItemProps {
  notification: NotificationData;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  isRealtime?: boolean;
}

export function NotificationItem({ notification, onMarkAsRead, onDelete, isRealtime = false }: NotificationItemProps) {
  const [isHovered, setIsHovered] = useState(false);

  const getNotificationIcon = () => {
    switch (notification.type) {
      case 'like':
        return <HeartSolidIcon className="h-5 w-5 text-red-500" />;
      case 'comment':
        return <ChatBubbleLeftIcon className="h-5 w-5 text-blue-500" />;
      case 'friend_request':
        return <UserPlusIcon className="h-5 w-5 text-green-500" />;
      case 'friend_accept':
        return <CheckIcon className="h-5 w-5 text-green-500" />;
      case 'subscription':
        return <UserPlusIcon className="h-5 w-5 text-blue-500" />;
      case 'subscription_back':
        return <UserPlusIcon className="h-5 w-5 text-blue-500" />;
      case 'message':
        return <EnvelopeIcon className="h-5 w-5 text-purple-500" />;
      case 'group_invite':
      case 'group_join_request':
      case 'group_join_approved':
      case 'group_post':
      case 'group_announcement':
        return <UsersIcon className="h-5 w-5 text-indigo-500" />;
      case 'event_invite':
      case 'event_reminder':
      case 'event_update':
      case 'event_comment':
        return <CalendarIcon className="h-5 w-5 text-orange-500" />;
      case 'store_follow':
      case 'store_review':
      case 'product_new':
      case 'product_report':
        return <ShoppingBagIcon className="h-5 w-5 text-yellow-500" />;
      case 'fan_page_follow':
      case 'fan_page_post':
      case 'fan_page_comment':
      case 'fan_page_like':
      case 'fan_page_role_added':
      case 'fan_page_message':
      case 'fan_page_reply':
        return <StarIcon className="h-5 w-5 text-pink-500" />;
      default:
        return <div className="h-5 w-5 rounded-full bg-gray-400" />;
    }
  };

  const getNotificationText = () => {
    const senderName = notification.sender?.name || 'Someone';

    switch (notification.type) {
      case 'like':
        return `${senderName} liked your post.`;
      case 'comment':
        return `${senderName} commented on your post.`;
      case 'friend_request':
        return `${senderName} sent you a friend request.`;
      case 'friend_accept':
        return `${senderName} accepted your friend request.`;
      case 'subscription':
        return `${senderName} subscribed to you.`;
      case 'subscription_back':
        return `${senderName} subscribed back to you.`;
      case 'message':
        return `${senderName} sent you a message.`;
      case 'group_invite':
        return `${senderName} invited you to join a group.`;
      case 'group_join_request':
        return `${senderName} requested to join your group.`;
      case 'group_join_approved':
        return `Your request to join a group was approved.`;
      case 'group_post':
        return `${senderName} posted in a group you're in.`;
      case 'group_announcement':
        return `${senderName} made an announcement in your group.`;
      case 'event_invite':
        return `${senderName} invited you to an event.`;
      case 'event_reminder':
        return `Reminder: You have an upcoming event.`;
      case 'event_update':
        return `An event you're attending has been updated.`;
      case 'event_comment':
        return `${senderName} commented on your event.`;
      case 'store_follow':
        return `${senderName} started following your store.`;
      case 'store_review':
        return `${senderName} reviewed your store.`;
      case 'product_new':
        return `A store you follow added a new product.`;
      case 'product_report':
        return `Your product was reported.`;
      case 'fan_page_follow':
        return `${senderName} started following your fan page.`;
      case 'fan_page_post':
        return `A fan page you follow made a new post.`;
      case 'fan_page_comment':
        return `${senderName} commented on your fan page post.`;
      case 'fan_page_like':
        return `${senderName} liked your fan page post.`;
      case 'fan_page_role_added':
        return `You were added as an admin to a fan page.`;
      case 'fan_page_message':
        return `${senderName} sent a message to your fan page.`;
      case 'fan_page_reply':
        return `Your fan page replied to your message.`;
      default:
        return `${senderName} interacted with your content.`;
    }
  };

  const getNotificationLink = () => {
    switch (notification.type) {
      case 'like':
      case 'comment':
        return notification.post ? `/posts/${notification.post.id}` : '#';
      case 'friend_request':
      case 'friend_accept':
        return notification.sender ? `/user/${notification.sender.id}` : '/friends';
      case 'subscription':
      case 'subscription_back':
        return notification.sender ? `/user/${notification.sender.id}` : '/connection';
      case 'message':
        return '/messages';
      case 'group_invite':
      case 'group_join_request':
      case 'group_join_approved':
      case 'group_post':
      case 'group_announcement':
        return notification.groupId ? `/groups/${notification.groupId}` : '/groups';
      case 'event_invite':
      case 'event_reminder':
      case 'event_update':
      case 'event_comment':
        return notification.eventId ? `/events/${notification.eventId}` : '/events';
      case 'store_follow':
      case 'store_review':
      case 'product_new':
      case 'product_report':
        return notification.storeId ? `/marketplace/stores/${notification.storeId}` : '/marketplace';
      case 'fan_page_follow':
      case 'fan_page_post':
      case 'fan_page_comment':
      case 'fan_page_like':
      case 'fan_page_role_added':
      case 'fan_page_message':
      case 'fan_page_reply':
        return notification.fanPageId ? `/fan-pages/${notification.fanPageId}` : '/pages';
      default:
        return '#';
    }
  };

  const handleClick = () => {
    if (!notification.read) {
      onMarkAsRead(notification.id);
    }
  };

  // Removed timeAgo variable as we'll use NotificationTime component

  return (
    <div
      className={cn(
        "relative flex items-start space-x-4 p-4 hover:bg-gray-50 transition-colors",
        !notification.read && "bg-blue-50 border-l-4 border-blue-500"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Avatar */}
      <div className="flex-shrink-0">
        {notification.sender?.image ? (
          <Image
            src={notification.sender.image}
            alt={notification.sender.name}
            width={40}
            height={40}
            className="rounded-full"
          />
        ) : (
          <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-gray-600 text-sm font-medium">
              {notification.sender?.name?.charAt(0) || '?'}
            </span>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <Link href={getNotificationLink()} onClick={handleClick}>
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-2">
              {/* Notification Icon */}
              <div className="flex-shrink-0 mt-1">
                {getNotificationIcon()}
              </div>

              {/* Text Content */}
              <div>
                <p className={cn(
                  "text-sm text-gray-900",
                  !notification.read && "font-semibold"
                )}>
                  {getNotificationText()}
                </p>
                <NotificationTime
                  date={notification.createdAt}
                  className="text-xs text-gray-500 mt-1"
                  autoUpdate={true}
                />
              </div>
            </div>

            {/* Unread indicator */}
            {!notification.read && (
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
              </div>
            )}
          </div>
        </Link>
      </div>

      {/* Actions Menu */}
      {isHovered && (
        <div className="absolute top-2 right-2">
          <Menu as="div" className="relative">
            <Menu.Button className="p-1 rounded-full hover:bg-gray-200 transition-colors">
              <EllipsisHorizontalIcon className="h-4 w-4 text-gray-500" />
            </Menu.Button>

            <Menu.Items className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
              <div className="py-1">
                {!notification.read && (
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => onMarkAsRead(notification.id)}
                        className={cn(
                          "block w-full text-left px-4 py-2 text-sm",
                          active ? "bg-gray-100 text-gray-900" : "text-gray-700"
                        )}
                      >
                        Mark as read
                      </button>
                    )}
                  </Menu.Item>
                )}
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={() => onDelete(notification.id)}
                      className={cn(
                        "block w-full text-left px-4 py-2 text-sm",
                        active ? "bg-gray-100 text-red-900" : "text-red-700"
                      )}
                    >
                      Delete notification
                    </button>
                  )}
                </Menu.Item>
              </div>
            </Menu.Items>
          </Menu>
        </div>
      )}
    </div>
  );
}
